"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Separator } from "@/components/ui/separator"
import {
  User,
  Calendar, 
  Phone, 
  Mail,
  MapPin,
  GraduationCap,
  Users, 
  FileText,
  Award,
  DollarSign,
  AlertCircle,
  Loader2
} from "lucide-react"
import { backendApi } from "@/lib/backend-api"
import { useToast } from "@/hooks/use-toast"

interface Student {
  id: string
  admissionNumber: string
  firstName: string
  lastName: string
  gender: string
  dateOfBirth: string
  email?: string
  phoneNumber?: string
  status: string
  bloodGroup?: string
  address?: string
  emergencyContact?: string
  medicalInfo?: string
  profileImageUrl?: string
  currentClass?: {
    id: string
    name: string
  }
  academicYear?: {
    id: string
    name: string
    isActive: boolean
  }
  parents?: Array<{
    relationship?: string
    isPrimary?: boolean
    parent?: {
      name: string
      email?: string
      phoneNumber?: string
    }
  }>
  recentAttendance?: Array<{
    id: string
    date: string
    status: string
    class: {
      name: string
    }
  }>
  recentGrades?: Array<{
    id: string
    subject: string
    grade: string
    score: number
    recordedAt: string
  }>
}

export default function StudentDetailsPage() {
  const params = useParams()
  const studentId = params.id as string
  const [student, setStudent] = useState<Student | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const { toast } = useToast()

  useEffect(() => {
    if (studentId) {
      fetchStudentDetails()
    }
  }, [studentId])

  const fetchStudentDetails = async () => {
    try {
      setIsLoading(true)
      console.log('🔍 fetchStudentDetails: Fetching student with ID:', studentId)
      
      const response = await backendApi.student.getStudentById(studentId)
      
      console.log('🔍 fetchStudentDetails: API response:', response)
      
      if (response.success && response.data?.student) {
        console.log('✅ fetchStudentDetails: Student found:', response.data.student)
        setStudent(response.data.student)
      } else {
        console.log('❌ fetchStudentDetails: No student data in response')
        toast({
          title: "Error",
          description: "Failed to fetch student details",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("❌ fetchStudentDetails: Error fetching student details:", error)
      toast({
        title: "Error",
        description: "Failed to fetch student details",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getAttendanceStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'PRESENT':
        return 'bg-green-100 text-green-800'
      case 'ABSENT':
        return 'bg-red-100 text-red-800'
      case 'LATE':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getGradeColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading student details...</span>
      </div>
    )
  }

  if (!student) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
        <h2 className="text-xl font-semibold text-gray-600 mb-2">Student Not Found</h2>
        <p className="text-gray-500 text-center">
          The student you're looking for could not be found or may have been removed.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage alt={student.firstName} src={student.profileImageUrl} />
            <AvatarFallback>
              {student.firstName.charAt(0)}{student.lastName.charAt(0)}
            </AvatarFallback>
          </Avatar>
        <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {student.firstName} {student.lastName}
            </h1>
            <p className="text-gray-500">
              Admission #{student.admissionNumber} • {student.currentClass?.name || 'No Class Assigned'}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            Export Profile
          </Button>
          <Button>
            <User className="mr-2 h-4 w-4" />
            Edit Profile
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="attendance">Attendance</TabsTrigger>
          <TabsTrigger value="grades">Grades</TabsTrigger>
          <TabsTrigger value="fees">Fees</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Full Name</p>
                    <p className="text-sm">{student.firstName} {student.lastName}</p>
              </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Gender</p>
                    <p className="text-sm">{student.gender}</p>
              </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Date of Birth</p>
                    <p className="text-sm">{student.dateOfBirth ? formatDate(student.dateOfBirth) : 'Not provided'}</p>
              </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Blood Group</p>
                    <p className="text-sm">{student.bloodGroup || 'Not provided'}</p>
              </div>
            </div>
                <Separator />
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{student.email || 'No email provided'}</span>
              </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{student.phoneNumber || 'No phone provided'}</span>
              </div>
                  {student.address && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{student.address}</span>
              </div>
                  )}
            </div>
          </CardContent>
        </Card>

            {/* Academic Information */}
            <Card>
          <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GraduationCap className="h-5 w-5" />
                  Academic Information
                </CardTitle>
          </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Current Class</p>
                    <p className="text-sm">{student.currentClass?.name || 'Not assigned'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Academic Year</p>
                    <p className="text-sm">{student.academicYear?.name || 'Not assigned'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    <Badge variant={student.status === "ACTIVE" ? "default" : "secondary"}>
                      {student.status}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Parent/Guardian Information */}
                <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Parent/Guardian Information
                </CardTitle>
                  </CardHeader>
                  <CardContent>
                {student.parents && student.parents.length > 0 ? (
                    <div className="space-y-4">
                    {student.parents.map((parent, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{parent.parent?.name || 'Parent Name'}</p>
                          {parent.isPrimary && (
                            <Badge variant="outline" className="text-xs">Primary</Badge>
                          )}
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-600">{parent.parent?.email || 'No email provided'}</p>
                          <p className="text-sm text-gray-600">{parent.parent?.phoneNumber || 'No phone provided'}</p>
                          {parent.relationship && (
                            <p className="text-sm text-gray-600">Relationship: {parent.relationship}</p>
                          )}
                        </div>
                        {index < (student.parents?.length || 0) - 1 && <Separator />}
                      </div>
                    ))}
                        </div>
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">No parent/guardian information available</p>
                        </div>
                )}
                  </CardContent>
                </Card>

            {/* Medical Information */}
                <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Medical Information
                </CardTitle>
                  </CardHeader>
                  <CardContent>
                {student.medicalInfo ? (
                  <div className="space-y-2">
                    <p className="text-sm">{student.medicalInfo}</p>
                    {student.emergencyContact && (
                      <div className="pt-2">
                        <p className="text-sm font-medium text-gray-500">Emergency Contact</p>
                        <p className="text-sm">{student.emergencyContact}</p>
                          </div>
                    )}
                        </div>
                ) : (
                  <div className="text-center py-4">
                    <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">No medical information available</p>
                    </div>
                )}
                  </CardContent>
                </Card>
          </div>
              </TabsContent>

        {/* Attendance Tab */}
        <TabsContent value="attendance" className="space-y-6">
                <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Recent Attendance
              </CardTitle>
                  </CardHeader>
                  <CardContent>
              {student.recentAttendance && student.recentAttendance.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Class</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {student.recentAttendance.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{formatDate(record.date)}</TableCell>
                          <TableCell>{record.class.name}</TableCell>
                          <TableCell>
                            <Badge className={getAttendanceStatusColor(record.status)}>
                              {record.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Attendance Records</h3>
                  <p className="text-gray-500">
                    Attendance records for this student are not yet available. 
                    Records will appear here once attendance is marked by teachers.
                  </p>
                    </div>
              )}
                  </CardContent>
                </Card>
              </TabsContent>

        {/* Grades Tab */}
        <TabsContent value="grades" className="space-y-6">
                <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Recent Grades
              </CardTitle>
                  </CardHeader>
                  <CardContent>
              {student.recentGrades && student.recentGrades.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Subject</TableHead>
                        <TableHead>Grade</TableHead>
                        <TableHead>Score</TableHead>
                        <TableHead>Date</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {student.recentGrades.map((grade) => (
                        <TableRow key={grade.id}>
                          <TableCell>{grade.subject}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{grade.grade}</Badge>
                          </TableCell>
                          <TableCell>
                            <span className={`font-medium ${getGradeColor(grade.score)}`}>
                              {grade.score}%
                            </span>
                          </TableCell>
                          <TableCell>{formatDate(grade.recordedAt)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                    </div>
              ) : (
                <div className="text-center py-8">
                  <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Grade Records</h3>
                  <p className="text-gray-500">
                    Grade records for this student are not yet available. 
                    Grades will appear here once they are recorded by teachers.
                  </p>
                          </div>
              )}
                  </CardContent>
                </Card>
              </TabsContent>

        {/* Fees Tab */}
        <TabsContent value="fees" className="space-y-6">
                <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Fee Information
              </CardTitle>
                  </CardHeader>
                  <CardContent>
              <div className="text-center py-8">
                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Fee Management Coming Soon</h3>
                <p className="text-gray-500">
                  Fee management functionality is currently under development. 
                  This section will display fee structures, payment history, and outstanding balances.
                </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
    </div>
  )
}
