import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHead<PERSON> } from "@/lib/auth"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Frontend Students GET: Request received");

    const auth = await authenticateUser(request)

    if (!auth.user) {
      console.log("❌ Frontend Students GET: Authentication failed");
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    console.log("✅ Frontend Students GET: User authenticated:", auth.user.id, auth.user.role);

    const { searchParams } = new URL(request.url)
    const page = searchParams.get("page") || "1"
    const limit = searchParams.get("limit") || "10"
    const search = searchParams.get("search") || ""
    const classId = searchParams.get("classId") || ""
    const status = searchParams.get("status") || ""

    // Build query parameters
    const queryParams = new URLSearchParams({
      page,
      limit,
      ...(search && { search }),
      ...(classId && { classId }),
      ...(status && { status })
    })

    console.log("🔍 Frontend Students GET: Query params:", queryParams.toString());

    // Forward the request to our backend API
    console.log("🔍 Frontend Students GET: Forwarding to backend...");
    const response = await fetch(`${BACKEND_URL}/api/v1/students?${queryParams}`, {
      headers: await createAuthHeaders(request),
    })

    console.log("📥 Frontend Students GET: Backend response status:", response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.log("❌ Frontend Students GET: Backend error:", errorData);
      return NextResponse.json({
        error: errorData.message || errorData.error || "Failed to fetch students"
      }, { status: response.status })
    }

    const data = await response.json()
    console.log("✅ Frontend Students GET: Success, students count:", data?.data?.students?.length || 'unknown');
    return NextResponse.json(data)
  } catch (error) {
    console.error("Students API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Frontend Students POST: Request received");

    const auth = await authenticateUser(request)

    if (!auth.user) {
      console.log("❌ Frontend Students POST: Authentication failed");
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    console.log("✅ Frontend Students POST: User authenticated:", auth.user.id, auth.user.role);

    const body = await request.json()
    console.log("🔍 Frontend Students POST: Request body:", JSON.stringify(body, null, 2));

    // Forward the request to our backend API
    console.log("🔍 Frontend Students POST: Forwarding to backend...");
    const response = await fetch(`${BACKEND_URL}/api/v1/students`, {
      method: "POST",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    console.log("📥 Frontend Students POST: Backend response status:", response.status);
    const data = await response.json()
    console.log("📥 Frontend Students POST: Backend response data:", JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.log("❌ Frontend Students POST: Backend returned error");
      return NextResponse.json({
        error: data.message || data.error || "Failed to create student"
      }, { status: response.status })
    }

    console.log("✅ Frontend Students POST: Success");
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Frontend Students POST: Error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
