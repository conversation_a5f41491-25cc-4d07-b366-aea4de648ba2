const { PrismaClient } = require('@prisma/client');
const { ValidationError, NotFoundError } = require('../../../utils/errors');

const prisma = new PrismaClient();

/**
 * Get all academic years
 */
const getAcademicYears = async (req, res) => {
  try {
    console.log('🔍 Getting academic years for user:', req.user.id, 'role:', req.user.role);
    console.log('🔍 User schoolId:', req.user.schoolId);
    console.log('🔍 User institutionId:', req.user.institutionId);

    const { page = 1, limit = 10, search = '', isActive } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build where clause with school filtering for multi-tenant support
    const where = {
      ...(search && {
        name: {
          contains: search,
          mode: 'insensitive'
        }
      }),
      ...(isActive !== undefined && {
        isActive: isActive === 'true'
      })
    };

    // Apply school-based filtering based on user role and context
    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can see all academic years
      console.log('🔍 Super admin - showing all academic years');
    } else if (req.user.role === 'SCHOOL_ADMIN' && req.user.schoolId) {
      // School admin can only see academic years from their school
      where.schoolId = req.user.schoolId;
      console.log('🔍 School admin - filtering by schoolId:', req.user.schoolId);
    } else if (req.user.institutionId) {
      // Institution admin can see academic years from all schools in their institution
      where.school = {
        institutionId: req.user.institutionId
      };
      console.log('🔍 Institution admin - filtering by institutionId:', req.user.institutionId);
    } else {
      // If no school/institution context, deny access
      console.log('❌ No school/institution context found for user');
      return res.status(403).json({
        success: false,
        message: 'Access denied: No school or institution association found'
      });
    }

    console.log('🔍 Final where clause for academic years:', JSON.stringify(where, null, 2));

    const [academicYears, total] = await Promise.all([
      prisma.academicYear.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: [
          { isActive: 'desc' },
          { startDate: 'desc' }
        ],
        include: {
          school: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              classes: true
            }
          }
        }
      }),
      prisma.academicYear.count({ where })
    ]);

    res.json({
      success: true,
      data: academicYears,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching academic years:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch academic years',
      error: error.message
    });
  }
};

/**
 * Get academic year by ID
 */
const getAcademicYearById = async (req, res) => {
  try {
    const { id } = req.params;

    const academicYear = await prisma.academicYear.findUnique({
      where: { id },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        classes: {
          select: {
            id: true,
            name: true,
            gradeLevel: true
          }
        }
      }
    });

    if (!academicYear) {
      throw new NotFoundError('Academic year not found');
    }

    res.json({
      success: true,
      data: academicYear
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    console.error('Error fetching academic year:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch academic year',
      error: error.message
    });
  }
};

/**
 * Create new academic year
 */
const createAcademicYear = async (req, res) => {
  try {
    const { name, startDate, endDate, isActive = false } = req.body;
    const userId = req.user.id;

    // Validation
    if (!name || !startDate || !endDate) {
      throw new ValidationError('Name, start date, and end date are required');
    }

    // Get user's school
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { school: true }
    });

    if (!user || !user.school) {
      throw new ValidationError('User must be associated with a school');
    }

    // If setting as active, deactivate other academic years for this school
    if (isActive) {
      await prisma.academicYear.updateMany({
        where: {
          schoolId: user.school.id,
          isActive: true
        },
        data: {
          isActive: false
        }
      });
    }

    const academicYear = await prisma.academicYear.create({
      data: {
        name,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        isActive,
        schoolId: user.school.id
      },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: academicYear,
      message: 'Academic year created successfully'
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    console.error('Error creating academic year:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create academic year',
      error: error.message
    });
  }
};

/**
 * Update academic year
 */
const updateAcademicYear = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, startDate, endDate, isActive } = req.body;

    const existingAcademicYear = await prisma.academicYear.findUnique({
      where: { id }
    });

    if (!existingAcademicYear) {
      throw new NotFoundError('Academic year not found');
    }

    // If setting as active, deactivate other academic years for this school
    if (isActive && !existingAcademicYear.isActive) {
      await prisma.academicYear.updateMany({
        where: {
          schoolId: existingAcademicYear.schoolId,
          isActive: true,
          id: { not: id }
        },
        data: {
          isActive: false
        }
      });
    }

    const academicYear = await prisma.academicYear.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(startDate && { startDate: new Date(startDate) }),
        ...(endDate && { endDate: new Date(endDate) }),
        ...(isActive !== undefined && { isActive })
      },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: academicYear,
      message: 'Academic year updated successfully'
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    console.error('Error updating academic year:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update academic year',
      error: error.message
    });
  }
};

/**
 * Delete academic year
 */
const deleteAcademicYear = async (req, res) => {
  try {
    const { id } = req.params;

    const academicYear = await prisma.academicYear.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            classes: true
          }
        }
      }
    });

    if (!academicYear) {
      throw new NotFoundError('Academic year not found');
    }

    // Check if academic year has associated classes
    if (academicYear._count.classes > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete academic year with associated classes'
      });
    }

    await prisma.academicYear.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Academic year deleted successfully'
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    console.error('Error deleting academic year:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete academic year',
      error: error.message
    });
  }
};

/**
 * Set academic year as active
 */
const setActiveAcademicYear = async (req, res) => {
  try {
    const { id } = req.params;

    const academicYear = await prisma.academicYear.findUnique({
      where: { id }
    });

    if (!academicYear) {
      throw new NotFoundError('Academic year not found');
    }

    // Deactivate all other academic years for this school
    await prisma.academicYear.updateMany({
      where: {
        schoolId: academicYear.schoolId,
        isActive: true
      },
      data: {
        isActive: false
      }
    });

    // Activate the selected academic year
    const updatedAcademicYear = await prisma.academicYear.update({
      where: { id },
      data: { isActive: true },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: updatedAcademicYear,
      message: 'Academic year set as active successfully'
    });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    console.error('Error setting active academic year:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set active academic year',
      error: error.message
    });
  }
};

module.exports = {
  getAcademicYears,
  getAcademicYearById,
  createAcademicYear,
  updateAcademicYear,
  deleteAcademicYear,
  setActiveAcademicYear
};
