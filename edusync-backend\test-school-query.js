const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testSchoolQuery() {
  try {
    const schoolId = 'cea77542-38ce-4a26-951c-3b54563c5d6c';
    console.log('Attempting to fetch school with ID:', schoolId);
    
    const school = await prisma.school.findUnique({
      where: { id: schoolId },
      include: {
        institution: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            users: true,
            fileUploads: true,
            academicYears: true,
            classes: true,
            subjects: true,
            departments: true,
          },
        },
      },
    });

    if (school) {
      console.log('School found successfully!');
      console.log('School details:', JSON.stringify(school, null, 2));
    } else {
      console.log('School not found in database');
    }
  } catch (error) {
    console.error('Error fetching school:');
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta
    });
  } finally {
    await prisma.$disconnect();
  }
}

testSchoolQuery(); 