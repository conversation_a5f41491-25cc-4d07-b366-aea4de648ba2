const { PrismaClient } = require('@prisma/client');
const { logger } = require('../../../utils/logger');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

/**
 * Get all students with filtering and pagination
 */
const getStudents = async (req, res) => {
  try {
    console.log('🔍 Getting students for user:', req.user.id, 'role:', req.user.role);
    console.log('🔍 User schoolId:', req.user.schoolId);
    console.log('🔍 User institutionId:', req.user.institutionId);

    const {
      page = 1,
      limit = 10,
      search,
      classId,
      status = 'ACTIVE',
      academicYearId,
      sortBy = 'admissionNumber',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause with school filtering for multi-tenant support
    let where = {
      studentStatus: status
    };

    // Apply school-based filtering based on user role and context
    let enrollmentFilter = { isActive: true };

    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can see all students
      console.log('🔍 Super admin - showing all students');
    } else if (req.user.role === 'SCHOOL_ADMIN' && req.user.schoolId) {
      // School admin can only see students from their school
      enrollmentFilter.schoolId = req.user.schoolId;
      console.log('🔍 School admin - filtering by schoolId:', req.user.schoolId);
    } else if (req.user.institutionId) {
      // Institution admin can see students from all schools in their institution
      enrollmentFilter.school = {
        institutionId: req.user.institutionId
      };
      console.log('🔍 Institution admin - filtering by institutionId:', req.user.institutionId);
    } else {
      // If no school/institution context, deny access
      console.log('❌ No school/institution context found for user');
      return res.status(403).json({
        success: false,
        message: 'Access denied: No school or institution association found'
      });
    }

    // Apply enrollment filtering to ensure students are from the correct school
    where.enrollments = {
      some: enrollmentFilter
    };

    // Add search functionality
    if (search) {
      where.OR = [
        { user: { firstName: { contains: search, mode: 'insensitive' } } },
        { user: { lastName: { contains: search, mode: 'insensitive' } } },
        { admissionNumber: { contains: search, mode: 'insensitive' } },
        { user: { email: { contains: search, mode: 'insensitive' } } }
      ];
    }

    // Filter by class if provided (additional filter)
    if (classId) {
      where.enrollments.some.classId = classId;
    }

    // Filter by academic year if provided (additional filter)
    if (academicYearId) {
      where.enrollments.some.academicYearId = academicYearId;
    }

    console.log('🔍 Final where clause:', JSON.stringify(where, null, 2));

    // Get students with enrollments and related data
    const students = await prisma.student.findMany({
      where,
      skip,
      take,
      orderBy: {
        [sortBy]: sortOrder
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
            profileImageUrl: true,
            isActive: true
          }
        },
        enrollments: {
          where: { isActive: true },
          include: {
            class: {
              select: {
                id: true,
                name: true,
                gradeLevel: true,
                section: true
              }
            },
            academicYear: {
              select: {
                id: true,
                name: true,
                isActive: true
              }
            }
          }
        },
        parents: {
          include: {
            parent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phoneNumber: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // Get total count for pagination
    const totalStudents = await prisma.student.count({ where });

    // Format response data
    const formattedStudents = students.map(student => ({
      id: student.id,
      admissionNumber: student.admissionNumber,
      firstName: student.user.firstName,
      lastName: student.user.lastName,
      email: student.user.email,
      phoneNumber: student.user.phoneNumber,
      profileImageUrl: student.user.profileImageUrl,
      dateOfBirth: student.dateOfBirth,
      gender: student.gender,
      status: student.studentStatus,
      bloodGroup: student.bloodGroup,
      address: student.address,
      emergencyContact: student.emergencyContact,
      medicalInfo: student.medicalConditions,
      currentClass: student.enrollments[0]?.class || null,
      academicYear: student.enrollments[0]?.academicYear || null,
      parents: student.parents.map(sp => ({
        relationship: sp.relationship,
        isPrimary: sp.isPrimary,
        parent: {
          name: `${sp.parent.user.firstName} ${sp.parent.user.lastName}`,
          email: sp.parent.user.email,
          phoneNumber: sp.parent.user.phoneNumber
        }
      })),
      createdAt: student.createdAt,
      updatedAt: student.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: {
        students: formattedStudents,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalStudents / take),
          totalStudents,
          limit: take
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching students:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch students',
      error: error.message
    });
  }
};

/**
 * Get student by ID
 */
const getStudentById = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('🔍 Backend getStudentById: Looking for student with ID:', id);

    const student = await prisma.student.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
            profileImageUrl: true,
            isActive: true
          }
        },
        enrollments: {
          include: {
            class: {
              include: {
                subjects: {
                  include: {
                    subject: true
                  }
                }
              }
            },
            academicYear: true
          }
        },
        parents: {
          include: {
            parent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phoneNumber: true
                  }
                }
              }
            }
          }
        },
        attendanceRecords: {
          take: 10,
          orderBy: { date: 'desc' }
        },
        gradeRecords: {
          take: 10,
          orderBy: { recordedAt: 'desc' },
          include: {
            subject: true
          }
        },
        examResults: {
          take: 10,
          orderBy: { submittedAt: 'desc' },
          include: {
            exam: true,
            subject: true
          }
        }
      }
    });

    if (!student) {
      console.log('❌ Backend getStudentById: Student not found with ID:', id);
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    console.log('✅ Backend getStudentById: Found student:', student.id);

    // Format response
    const formattedStudent = {
      id: student.id,
      admissionNumber: student.admissionNumber,
      firstName: student.user.firstName,
      lastName: student.user.lastName,
      email: student.user.email,
      phoneNumber: student.user.phoneNumber,
      profileImageUrl: student.user.profileImageUrl,
      dateOfBirth: student.dateOfBirth,
      gender: student.gender,
      status: student.studentStatus,
      bloodGroup: student.bloodGroup,
      address: student.address,
      emergencyContact: student.emergencyContact,
      medicalInfo: student.medicalInfo,
      currentClass: student.enrollments?.[0]?.class || null,
      academicYear: student.enrollments?.[0]?.academicYear || null,
      enrollments: student.enrollments,
      parents: student.parents,
      recentAttendance: student.attendanceRecords,
      recentGrades: student.gradeRecords,
      recentExamResults: student.examResults,
      createdAt: student.createdAt,
      updatedAt: student.updatedAt
    };

    console.log('✅ Backend getStudentById: Returning formatted student data');

    res.status(200).json({
      success: true,
      data: { student: formattedStudent }
    });

  } catch (error) {
    console.error('❌ Backend getStudentById: Error:', error);
    logger.error('Error fetching student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student',
      error: error.message
    });
  }
};

/**
 * Create new student
 */
const createStudent = async (req, res) => {
  try {
    console.log('🔍 Creating student - Request body:', JSON.stringify(req.body, null, 2));
    console.log('🔍 Creating student - User context:', {
      id: req.user.id,
      role: req.user.role,
      schoolId: req.user.schoolId,
      institutionId: req.user.institutionId
    });

    const {
      firstName,
      lastName,
      email,
      phoneNumber,
      dateOfBirth,
      gender,
      admissionNumber,
      classId,
      academicYearId,
      bloodGroup,
      address,
      emergencyContact,
      medicalInfo,
      parents = []
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !admissionNumber || !classId || !academicYearId) {
      console.log('❌ Missing required fields:', {
        firstName: !!firstName,
        lastName: !!lastName,
        admissionNumber: !!admissionNumber,
        classId: !!classId,
        academicYearId: !!academicYearId
      });
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: firstName, lastName, admissionNumber, classId, academicYearId'
      });
    }

    // Check if admission number already exists
    const existingStudent = await prisma.student.findUnique({
      where: { admissionNumber }
    });

    if (existingStudent) {
      console.log('❌ Admission number already exists:', admissionNumber);
      return res.status(400).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Validate class exists and user has access to it
    console.log('🔍 Validating class access for classId:', classId);
    const classData = await prisma.class.findUnique({
      where: { id: classId },
      select: {
        id: true,
        name: true,
        schoolId: true,
        school: {
          select: {
            id: true,
            name: true,
            institutionId: true
          }
        }
      }
    });

    if (!classData) {
      console.log('❌ Class not found:', classId);
      return res.status(400).json({
        success: false,
        message: 'Class not found'
      });
    }

    console.log('🔍 Found class:', classData);

    // Check if user has access to this class's school
    if (req.user.role === 'SCHOOL_ADMIN' && req.user.schoolId !== classData.schoolId) {
      console.log('❌ School admin trying to access different school class:', {
        userSchoolId: req.user.schoolId,
        classSchoolId: classData.schoolId
      });
      return res.status(403).json({
        success: false,
        message: 'Access denied: You can only create students in your school'
      });
    }

    // Validate academic year exists
    console.log('🔍 Validating academic year:', academicYearId);
    const academicYear = await prisma.academicYear.findUnique({
      where: { id: academicYearId },
      select: {
        id: true,
        name: true,
        schoolId: true,
        isActive: true
      }
    });

    if (!academicYear) {
      console.log('❌ Academic year not found:', academicYearId);
      return res.status(400).json({
        success: false,
        message: 'Academic year not found'
      });
    }

    console.log('🔍 Found academic year:', academicYear);

    // Ensure academic year belongs to the same school as the class
    if (academicYear.schoolId !== classData.schoolId) {
      console.log('❌ Academic year and class belong to different schools:', {
        academicYearSchoolId: academicYear.schoolId,
        classSchoolId: classData.schoolId
      });
      return res.status(400).json({
        success: false,
        message: 'Academic year and class must belong to the same school'
      });
    }

    // Create student in a transaction
    console.log('🔍 Starting transaction to create student');
    const result = await prisma.$transaction(async (tx) => {

      // Generate a default password for the student
      const defaultPassword = `${admissionNumber}123`; // Default password: admissionNumber + "123"
      const passwordHash = await bcrypt.hash(defaultPassword, 12);

      console.log('🔍 Creating user with email:', email || `${admissionNumber}@student.school.edu`);

      // Create user first
      const user = await tx.user.create({
        data: {
          firstName,
          lastName,
          email: email || `${admissionNumber}@student.school.edu`,
          phoneNumber,
          passwordHash,
          role: 'STUDENT',
          isEmailVerified: false
        }
      });

      console.log('✅ User created with ID:', user.id);

      // Create student
      const student = await tx.student.create({
        data: {
          userId: user.id,
          admissionNumber,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          gender,
          bloodGroup,
          address,
          emergencyContact,
          medicalInfo,
          studentStatus: 'ACTIVE'
        }
      });

      console.log('✅ Student created with ID:', student.id);

      // Create enrollment
      const enrollment = await tx.studentEnrollment.create({
        data: {
          studentId: student.id,
          classId,
          academicYearId,
          schoolId: classData.schoolId,
          enrollmentNumber: `ENR-${admissionNumber}-${new Date().getFullYear()}`,
          enrollmentDate: new Date(),
          isActive: true
        }
      });

      console.log('✅ Enrollment created with ID:', enrollment.id);

      return { user, student, enrollment };
    });

    logger.info(`Student created: ${result.student.id}`);

    res.status(201).json({
      success: true,
      message: 'Student created successfully',
      data: { student: result.student }
    });

  } catch (error) {
    console.error('❌ Error creating student:', error);
    logger.error('Error creating student:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      const target = error.meta?.target;
      if (target?.includes('email')) {
        return res.status(400).json({
          success: false,
          message: 'A user with this email already exists'
        });
      }
      if (target?.includes('admissionNumber')) {
        return res.status(400).json({
          success: false,
          message: 'A student with this admission number already exists'
        });
      }
    }

    if (error.code === 'P2003') {
      return res.status(400).json({
        success: false,
        message: 'Invalid reference: Class or Academic Year not found'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create student',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Update student
 */
const updateStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    console.log('🔍 Backend updateStudent: Starting update for student ID:', id);
    console.log('🔍 Backend updateStudent: Update data:', updateData);

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id },
      include: { user: true }
    });

    if (!existingStudent) {
      console.log('❌ Backend updateStudent: Student not found:', id);
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    console.log('✅ Backend updateStudent: Found existing student:', existingStudent.id);

    // Update student and user in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update user data if provided
      if (updateData.firstName || updateData.lastName || updateData.email || updateData.phoneNumber) {
        console.log('🔍 Backend updateStudent: Updating user data...');
        await tx.user.update({
          where: { id: existingStudent.userId },
          data: {
            ...(updateData.firstName && { firstName: updateData.firstName }),
            ...(updateData.lastName && { lastName: updateData.lastName }),
            ...(updateData.email && { email: updateData.email }),
            ...(updateData.phoneNumber && { phoneNumber: updateData.phoneNumber })
          }
        });
      }

      // Update student data
      console.log('🔍 Backend updateStudent: Updating student data...');
      const student = await tx.student.update({
        where: { id },
        data: {
          ...(updateData.dateOfBirth && { dateOfBirth: new Date(updateData.dateOfBirth) }),
          ...(updateData.gender && { gender: updateData.gender }),
          ...(updateData.bloodGroup && { bloodGroup: updateData.bloodGroup }),
          ...(updateData.address && { address: updateData.address }),
          ...(updateData.emergencyContact && { emergencyContact: updateData.emergencyContact }),
          ...(updateData.medicalInfo && { medicalInfo: updateData.medicalInfo }),
          ...(updateData.studentStatus && { studentStatus: updateData.studentStatus })
        }
      });

      return student;
    });

    console.log('✅ Backend updateStudent: Update successful for student:', id);

    logger.info(`Student updated: ${id}`);

    res.status(200).json({
      success: true,
      message: 'Student updated successfully',
      data: { student: result }
    });

  } catch (error) {
    console.error('❌ Backend updateStudent: Error:', error);
    logger.error('Error updating student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update student',
      error: error.message
    });
  }
};

/**
 * Delete student
 */
const deleteStudent = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id }
    });

    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Soft delete by updating status
    await prisma.student.update({
      where: { id },
      data: { studentStatus: 'INACTIVE' }
    });

    logger.info(`Student deleted: ${id}`);

    res.status(200).json({
      success: true,
      message: 'Student deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete student',
      error: error.message
    });
  }
};

/**
 * Get student statistics
 */
const getStudentStats = async (req, res) => {
  try {
    const { institutionId } = req.query;

    const [
      totalStudents,
      activeStudents,
      inactiveStudents,
      suspendedStudents,
      graduatedStudents
    ] = await Promise.all([
      prisma.student.count({
        where: { institutionId }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          studentStatus: 'ACTIVE' 
        }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          studentStatus: 'INACTIVE' 
        }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          studentStatus: 'SUSPENDED' 
        }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          studentStatus: 'GRADUATED' 
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        total: totalStudents,
        active: activeStudents,
        inactive: inactiveStudents,
        suspended: suspendedStudents,
        graduated: graduatedStudents
      }
    });
  } catch (error) {
    logger.error('Error fetching student statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student statistics',
      error: error.message
    });
  }
};

/**
 * Bulk create students
 */
const bulkCreateStudents = async (req, res) => {
  try {
    const { students } = req.body;
    
    if (!Array.isArray(students) || students.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Students array is required and must not be empty'
      });
    }

    const createdStudents = await prisma.student.createMany({
      data: students,
      skipDuplicates: true
    });

    res.status(201).json({
      success: true,
      message: `Successfully created ${createdStudents.count} students`,
      data: createdStudents
    });
  } catch (error) {
    logger.error('Error bulk creating students:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk create students',
      error: error.message
    });
  }
};

/**
 * Get student attendance
 */
const getStudentAttendance = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.query;

    const where = {
      studentId: id
    };

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    }

    const attendance = await prisma.attendance.findMany({
      where,
      include: {
        class: {
          select: {
            name: true,
            subject: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    res.json({
      success: true,
      data: attendance
    });
  } catch (error) {
    logger.error('Error fetching student attendance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student attendance',
      error: error.message
    });
  }
};

/**
 * Get student grades
 */
const getStudentGrades = async (req, res) => {
  try {
    const { id } = req.params;
    const grades = await prisma.gradeRecord.findMany({
      where: { studentId: id },
      include: {
        subject: { select: { id: true, name: true, code: true } },
        academicYear: { select: { id: true, name: true } },
        recordedBy: { include: { user: { select: { firstName: true, lastName: true } } } },
      },
      orderBy: { recordedAt: 'desc' },
    });
    res.json({ success: true, data: grades });
  } catch (error) {
    logger.error('Error fetching student grades:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch student grades', error: error.message });
  }
};

/**
 * Get student fees
 */
const getStudentFees = async (req, res) => {
  try {
    const { id } = req.params;
    
    const fees = await prisma.feeRecord.findMany({
      where: {
        studentId: id
      },
      include: {
        feeStructure: {
          select: {
            name: true,
            amount: true,
            dueDate: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: fees
    });
  } catch (error) {
    logger.error('Error fetching student fees:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student fees',
      error: error.message
    });
  }
};

/**
 * Get student enrollments
 */
const getStudentEnrollments = async (req, res) => {
  try {
    const { id } = req.params;
    
    const enrollments = await prisma.enrollment.findMany({
      where: {
        studentId: id
      },
      include: {
        class: {
          select: {
            name: true,
            grade: true,
            section: true
          }
        },
        academicYear: {
          select: {
            name: true,
            startDate: true,
            endDate: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: enrollments
    });
  } catch (error) {
    logger.error('Error fetching student enrollments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student enrollments',
      error: error.message
    });
  }
};

module.exports = {
  getStudents,
  getStudentById,
  createStudent,
  updateStudent,
  deleteStudent,
  getStudentStats,
  bulkCreateStudents,
  getStudentAttendance,
  getStudentGrades,
  getStudentFees,
  getStudentEnrollments
};