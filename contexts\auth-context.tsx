"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useRouter } from "next/navigation"

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  isEmailVerified: boolean
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  authError: string | null
  login: (email: string, password: string, mfaToken?: string) => Promise<{
    success: boolean
    user?: User
    error?: string
    mfaRequired?: boolean
  }>
  logout: () => Promise<void>
  refreshToken: () => Promise<string | null>
  clearError: () => void
  retryAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [authError, setAuthError] = useState<string | null>(null)
  const router = useRouter()

  // Set up automatic token refresh
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout

    if (user) {
      // Check token every 10 minutes and refresh if needed
      refreshInterval = setInterval(async () => {
      
        try {
          const response = await fetch("/api/auth/session", {
            credentials: 'include'
          })

          if (response.status === 401) {
          
            const newToken = await refreshToken()
            if (!newToken) {
            
              await logout()
            } else {
           
              // Re-check auth after successful refresh
              await checkAuth()
            }
          }
        } catch (error) {
          console.error("❌ AuthProvider: Token check failed:", error)
        }
      }, 10 * 60 * 1000) // 10 minutes
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [user])

  // Initialize authentication on mount with retry logic
  useEffect(() => {
    let mounted = true
    let retryCount = 0
    const maxRetries = 3

    const initializeAuth = async () => {
      while (mounted && retryCount < maxRetries) {
        try {
          await checkAuth()
          break // Success, exit retry loop
        } catch (error) {
          retryCount++
          console.warn(`Auth initialization attempt ${retryCount} failed:`, error)

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000))
          } else {
            console.error("Auth initialization failed after all retries")
            setAuthError("Failed to initialize authentication")
            setIsLoading(false)
          }
        }
      }
    }

    initializeAuth()

    return () => {
      mounted = false
    }
  }, [])

  const checkAuth = async () => {
    try {
      setAuthError(null) // Clear any previous errors

      const response = await fetch("/api/auth/session", {
        credentials: 'include',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle different error scenarios
        if (response.status === 401) {
          
          setUser(null)
          setIsLoading(false)
          return
        }
        throw new Error(`Auth check failed: ${response.status} - ${data.error || 'Unknown error'}`)
      }

      if (data.authenticated && data.user) {
       
        setUser(data.user)

        // Warn if session is not fully valid
        if (!data.sessionValid) {
          console.warn("⚠️ AuthProvider: Session validation warning - tokens may need refresh")
          setAuthError("Session may need refresh")
        }
      } else {
        
        setUser(null)
      }
    } catch (error) {
      console.error("❌ AuthProvider: Auth check failed:", error)
      setUser(null)
      setAuthError(error instanceof Error ? error.message : "Authentication check failed")

      // Don't throw the error here to prevent error boundary from catching it
      // This is expected behavior when user is not authenticated
    } finally {
      setIsLoading(false)
    }
  }

  const refreshToken = async () => {
    try {
      const response = await fetch("/api/auth/refresh", {
        method: "POST",
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        return data.accessToken
      }
      return null
    } catch (error) {
      console.error("Token refresh error:", error)
      return null
    }
  }

  const login = async (email: string, password: string, mfaToken?: string) => {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include',
        body: JSON.stringify({ email, password, mfaToken }),
      })

      const data = await response.json()

      if (response.ok) {
        setUser(data.user)
        return { success: true, user: data.user }
      } else {
        return {
          success: false,
          error: data.message || "Login failed",
          mfaRequired: data.mfaRequired,
        }
      }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, error: "Network error. Please check your connection and try again." }
    }
  }

  const logout = async () => {
    try {
   

      // Clear local state immediately
      setUser(null)
      setAuthError(null)

      // Call logout API
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: 'include'
      })

  
    } catch (error) {
      console.error("❌ AuthProvider: Logout API failed:", error)
      // Continue with local cleanup even if API fails
    } finally {
      // Clear any localStorage items that might contain auth data
      try {
        localStorage.removeItem("isLoggedIn")
        localStorage.removeItem("currentUser")
        localStorage.removeItem("currentInstitutionId")
        localStorage.removeItem("authToken")
        localStorage.removeItem("refreshToken")
      } catch (storageError) {
        console.warn("Failed to clear localStorage:", storageError)
      }

      // Redirect to login page
      router.push("/auth/login")
    }
  }

  const clearError = () => {
    setAuthError(null)
  }

  const retryAuth = async () => {
    setIsLoading(true)
    await checkAuth()
  }

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      isAuthenticated: !!user,
      authError,
      login,
      logout,
      refreshToken,
      clearError,
      retryAuth
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
} 