const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function checkUserPassword() {
  try {
    console.log('🔍 Checking institution admin user...');
    
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        passwordHash: true
      }
    });
    
    if (user) {
      console.log('✅ User found:', user.email, user.role);
      console.log('Password hash:', user.passwordHash);
      
      // Test common passwords
      const testPasswords = ['password123', 'admin123', '123456', 'password', 'admin'];
      
      for (const password of testPasswords) {
        const isValid = await bcrypt.compare(password, user.passwordHash);
        if (isValid) {
          console.log(`✅ Password found: ${password}`);
          break;
        }
      }
    } else {
      console.log('❌ User not found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUserPassword(); 