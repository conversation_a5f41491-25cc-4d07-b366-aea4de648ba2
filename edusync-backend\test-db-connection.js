const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testConnection() {
  try {
    console.log('Attempting to connect to database...');
    // Try to query the database
    const result = await prisma.$queryRaw`SELECT 1`;
    console.log('Database connection successful!');
    console.log('Result:', result);
  } catch (error) {
    console.error('Database connection failed!');
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta
    });
  } finally {
    await prisma.$disconnect();
  }
}

testConnection(); 