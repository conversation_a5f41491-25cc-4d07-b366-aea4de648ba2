const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function associateCurrentUser() {
  try {
    // Get the first user (assuming this is the current logged-in user)
    const users = await prisma.user.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' }
    });
    
    console.log('Available users:');
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.role}) - ID: ${user.id}`);
    });
    
    // Get the test institution
    const institution = await prisma.institution.findUnique({
      where: { id: 'test-institution-123' }
    });
    
    if (!institution) {
      console.log('Test institution not found. Creating it...');
      const newInstitution = await prisma.institution.create({
        data: {
          id: "test-institution-123",
          name: "Test Institution",
          email: "<EMAIL>",
          logo: null,
          subscriptionStatus: "TRIAL",
          subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          isActive: true,
          address: "123 Education Street",
          city: "Learning City",
          state: "Knowledge State",
          country: "Education Country",
          phoneNumber: "+****************",
          website: "https://test.edu"
        }
      });
      console.log('Created test institution:', newInstitution.id);
    } else {
      console.log('Test institution found:', institution.id);
    }
    
    // Associate the first user with the institution
    if (users.length > 0) {
      const user = users[0];
      
      // Check if association already exists
      const existingAssociation = await prisma.institutionUser.findFirst({
        where: {
          userId: user.id,
          institutionId: 'test-institution-123'
        }
      });
      
      if (existingAssociation) {
        console.log(`User ${user.email} is already associated with the institution`);
      } else {
        const association = await prisma.institutionUser.create({
          data: {
            userId: user.id,
            institutionId: 'test-institution-123',
            role: user.role
          }
        });
        console.log(`Associated user ${user.email} with institution:`, association);
      }
    }
    
    console.log('Setup complete!');
    
  } catch (error) {
    console.error('Error associating user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

associateCurrentUser(); 