const express = require('express');
const router = express.Router();
const dashboardController = require('./dashboard.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/v1/dashboard/stats - Get dashboard statistics
router.get('/stats', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), dashboardController.getDashboardStats);

// GET /api/v1/dashboard/activities - Get recent activities
router.get('/activities', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), dashboardController.getRecentActivities);

// GET /api/v1/dashboard/analytics - Get analytics data
router.get('/analytics', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER']), dashboardController.getAnalytics);

module.exports = router;
