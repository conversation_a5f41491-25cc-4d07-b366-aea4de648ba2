const { PrismaClient } = require('@prisma/client');
const AppError = require('../utils/appError');

const prisma = new PrismaClient();

/**
 * Middleware to inject school/institution context for multi-tenant support
 * This ensures users can only access data from their associated schools/institutions
 */
exports.injectSchoolContext = async (req, res, next) => {
  try {
    console.log('🔍 SchoolContext Middleware: Processing request for user:', req.user?.id, 'role:', req.user?.role);

    if (!req.user) {
      return next(new AppError('Authentication required', 401));
    }

    // Initialize context object
    req.schoolContext = {
      schoolIds: null,
      institutionIds: null,
      canAccessAllSchools: false,
      canAccessAllInstitutions: false
    };

    // Super admin has access to everything
    if (req.user.role === 'SUPER_ADMIN') {
      console.log('  - Super admin: granting access to all schools and institutions');
      req.schoolContext.canAccessAllSchools = true;
      req.schoolContext.canAccessAllInstitutions = true;
      return next();
    }

    // For school admin users, restrict to their specific school
    if (req.user.role === 'SCHOOL_ADMIN' && req.user.schoolId) {
      req.schoolContext.schoolIds = [req.user.schoolId];
      console.log('  - School admin: restricting to schoolId:', req.schoolContext.schoolIds);
      return next();
    }

    // For institution admin, get all schools in their institution
    if (req.user.institutionId) {
      req.schoolContext.institutionIds = [req.user.institutionId];
      console.log('  - Institution admin: restricting to institutionId:', req.schoolContext.institutionIds);
      return next();
    }

    // Use enhanced user context from authentication middleware
    if (req.user.institutions && req.user.institutions.length > 0) {
      req.schoolContext.institutionIds = req.user.institutions.map(inst => inst.institutionId);
      console.log('  - Using institutions from user context:', req.schoolContext.institutionIds);
      return next();
    }

    if (req.user.schools && req.user.schools.length > 0) {
      req.schoolContext.schoolIds = req.user.schools.map(school => school.schoolId);
      console.log('  - Using schools from user context:', req.schoolContext.schoolIds);
      return next();
    }

    // Fallback to database lookup
    console.log('  - No school/institution context found, performing database lookup');
    const userInstitutions = await prisma.institutionUser.findMany({
      where: {
        userId: req.user.id,
        isActive: true
      },
      select: { institutionId: true }
    });

    if (userInstitutions.length > 0) {
      req.schoolContext.institutionIds = userInstitutions.map(ui => ui.institutionId);
      console.log('  - Found institutions from database:', req.schoolContext.institutionIds);
      return next();
    }

    // If no access context found, deny access
    console.log('❌ No school/institution association found for user');
    return next(new AppError('Access denied: No school or institution association found', 403));

  } catch (error) {
    console.error('❌ SchoolContext Middleware error:', error);
    return next(new AppError('Failed to determine school context', 500));
  }
};

/**
 * Helper function to build Prisma where clause with school/institution filtering
 * @param {Object} schoolContext - The school context from middleware
 * @param {Object} baseWhere - Base where clause to extend
 * @param {Object} options - Options for filtering
 * @param {string} options.schoolIdField - Field name for school ID (default: 'schoolId')
 * @param {string} options.institutionPath - Path to institution relation (default: 'school.institutionId')
 * @returns {Object} Extended where clause with school/institution filtering
 */
exports.buildSchoolFilteredWhere = (schoolContext, baseWhere = {}, options = {}) => {
  const { schoolIdField = 'schoolId', institutionPath = 'school.institutionId' } = options;
  
  // If user can access all schools/institutions, return base where clause
  if (schoolContext.canAccessAllSchools || schoolContext.canAccessAllInstitutions) {
    return baseWhere;
  }

  const where = { ...baseWhere };

  // Apply school filtering
  if (schoolContext.schoolIds) {
    where[schoolIdField] = { in: schoolContext.schoolIds };
  } else if (schoolContext.institutionIds) {
    // Build nested where clause for institution filtering
    const pathParts = institutionPath.split('.');
    let nestedWhere = { in: schoolContext.institutionIds };
    
    // Build the nested structure backwards
    for (let i = pathParts.length - 1; i >= 0; i--) {
      const part = pathParts[i];
      if (i === pathParts.length - 1) {
        // Last part gets the actual filter
        nestedWhere = { [part]: nestedWhere };
      } else {
        // Intermediate parts wrap the filter
        nestedWhere = { [part]: nestedWhere };
      }
    }
    
    Object.assign(where, nestedWhere);
  }

  return where;
};

/**
 * Helper function to validate if user has access to a specific school
 * @param {Object} schoolContext - The school context from middleware
 * @param {string} schoolId - The school ID to validate access for
 * @returns {boolean} True if user has access, false otherwise
 */
exports.validateSchoolAccess = (schoolContext, schoolId) => {
  // Super admin can access any school
  if (schoolContext.canAccessAllSchools) {
    return true;
  }

  // Check if user has direct access to the school
  if (schoolContext.schoolIds && schoolContext.schoolIds.includes(schoolId)) {
    return true;
  }

  // For institution-level access, we'd need to check if the school belongs to user's institution
  // This would require a database query, so it's better to handle this in the controller
  return false;
};

/**
 * Middleware specifically for school admin users to ensure they only access their school's data
 */
exports.restrictToUserSchool = (req, res, next) => {
  if (req.user.role === 'SCHOOL_ADMIN') {
    if (!req.user.schoolId) {
      return next(new AppError('School admin user must have an associated school', 403));
    }
    
    // Override any schoolId in query params to ensure school admin can only access their school
    if (req.query.schoolId && req.query.schoolId !== req.user.schoolId) {
      console.log('⚠️ School admin attempted to access different school, overriding schoolId');
    }
    req.query.schoolId = req.user.schoolId;
  }
  
  next();
};
