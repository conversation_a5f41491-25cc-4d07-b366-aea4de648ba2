import { NextRequest } from 'next/server';

export async function GET(req: NextRequest, context: { params: { id: string } } | Promise<{ params: { id: string } }>) {
  // Await params if it's a promise (Next.js 13+)
  const { params } = await Promise.resolve(context);
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000';
  const { id } = params;
  const url = `${backendUrl}/api/v1/students/${id}/grades`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      cookie: req.headers.get('cookie') || '',
    },
    credentials: 'include',
  });
  const data = await response.text();
  return new Response(data, {
    status: response.status,
    headers: { 'Content-Type': 'application/json' },
  });
} 