"use client"

import { useState, useEffect } from "react"
import { PlusCircle, Search, Loader2, MoreHorizontal, Edit, Users, Trash2 } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { backendApi, safeArrayFromResponse } from "@/lib/backend-api"
import { enhancedApiClient } from "@/lib/api-client-enhanced"
import { useToast } from "@/hooks/use-toast"

// Interfaces
interface Class {
  id: string
  name: string
  gradeLevel: string
  section?: string
  capacity?: number
  roomNumber?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  school: {
    id: string
    name: string
  }
  academicYear: {
    id: string
    name: string
    isActive: boolean
  }
  studentEnrollments?: Array<{
    id: string
    student: {
      id: string
      firstName: string
      lastName: string
    }
  }>
  subjects?: Array<{
    id: string
    subject: {
      id: string
      name: string
      code?: string
    }
  }>
}

interface Subject {
  id: string
  name: string
  code?: string
  description?: string
  isCore: boolean
  isActive: boolean
}

interface AcademicYear {
  id: string
  name: string
  startDate: string
  endDate: string
  isActive: boolean
}

// Grade levels for dropdown
const gradeOptions = [
  "Kindergarten",
  "1st Grade",
  "2nd Grade",
  "3rd Grade",
  "4th Grade",
  "5th Grade",
  "6th Grade",
  "7th Grade",
  "8th Grade",
  "9th Grade",
  "10th Grade",
  "11th Grade",
  "12th Grade",
  "Mixed",
]

function ClassesContent() {
  const [classes, setClasses] = useState<Class[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddClassOpen, setIsAddClassOpen] = useState(false)
  const [isEditClassOpen, setIsEditClassOpen] = useState(false)
  const [editingClass, setEditingClass] = useState<Class | null>(null)
  const [filteredClasses, setFilteredClasses] = useState<Class[]>([])
  const [newClass, setNewClass] = useState({
    name: "",
    gradeLevel: "",
    section: "",
    capacity: "",
    roomNumber: "",
    academicYearId: "",
    academicYearName: "",
    subjects: [] as string[]
  })
  const { toast } = useToast()

  useEffect(() => {
    console.log("Classes page mounted, fetching data...")
    fetchClasses()
    fetchSubjects()
    fetchAcademicYears()
  }, [])

  useEffect(() => {
    console.log("📊 Classes state changed, updating filtered classes")
    console.log("📊 New classes count:", classes.length)
    console.log("📊 Classes sample:", classes.slice(0, 2))
    setFilteredClasses(classes)
    console.log("✅ Filtered classes updated")
  }, [classes])

  const fetchClasses = async () => {
    try {
      console.log("🔄 Fetching classes...")
      console.log("📊 Current classes state before fetch:", classes.length, "classes")
      setIsLoading(true)

      // Use the enhanced API client which goes through the frontend API route
      const response = await backendApi.class.getClasses()
      console.log("📥 API response:", response)
      console.log("📥 API response type:", typeof response)
      console.log("📥 API response keys:", response ? Object.keys(response) : 'null')
      console.log("📥 API response success:", response?.success)
      console.log("📥 API response data:", response?.data)

      let classesData = []
      let isSuccess = false

      // Handle different response formats
      if (response && response.success) {
        isSuccess = true
        if (response.data && response.data.classes) {
          classesData = response.data.classes
        } else if (Array.isArray(response.data)) {
          classesData = response.data
        }
      } else if (response && Array.isArray(response)) {
        // Direct array response
        classesData = response
        isSuccess = true
      }

      console.log("📊 Processed classes data:", classesData)
      console.log("📊 Classes data length:", classesData.length)
      console.log("📊 Classes data sample:", classesData.slice(0, 2))

      console.log("🔄 Setting classes state...")
      setClasses(classesData)
      setFilteredClasses(classesData)
      console.log("✅ Classes state updated")

      if (isSuccess) {
        // Extract schoolId from the first class for new class creation
        if (classesData.length > 0 && classesData[0].school) {
          setNewClass(prev => ({ ...prev, schoolId: classesData[0].school.id }))
          console.log("Set schoolId:", classesData[0].school.id)
        }
      } else {
        console.log("Failed to fetch classes:", response?.error || 'Unknown error')
        if (response?.error) {
          toast({
            title: "Error",
            description: response.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error("Error fetching classes:", error)
      // Arrays are already set to safe values above
      toast({
        title: "Error",
        description: `Failed to fetch classes: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchSubjects = async () => {
    try {
      const response = await backendApi.subject.getSubjects()
      const subjectsData = safeArrayFromResponse(response)
      setSubjects(subjectsData)

      if (response && !response.success) {
        console.error("Failed to fetch subjects:", response.error)
      }
    } catch (error) {
      console.error("Error fetching subjects:", error)
      setSubjects([])
    }
  }

  const fetchAcademicYears = async () => {
    try {
      const response = await backendApi.academicYear.getAcademicYears()
      const academicYearsData = safeArrayFromResponse(response)
      setAcademicYears(academicYearsData)

      // Set default academic year to active one
      const activeYear = academicYearsData.find((year: any) => year.isActive)
      if (activeYear && !newClass.academicYearId) {
        setNewClass(prev => ({
          ...prev,
          academicYearId: activeYear.id,
          academicYearName: activeYear.name
        }))
      }

      if (response && !response.success) {
        console.error("Failed to fetch academic years:", response.error)
      }
    } catch (error) {
      console.error("Error fetching academic years:", error)
      setAcademicYears([])
    }
  }

  // Filter classes based on search term
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    if (term === "") {
      setFilteredClasses(classes)
      return
    }

    const filtered = classes.filter(
      (cls) =>
        cls.name.toLowerCase().includes(term.toLowerCase()) ||
        cls.gradeLevel.toLowerCase().includes(term.toLowerCase()) ||
        cls.section?.toLowerCase().includes(term.toLowerCase()) ||
        cls.roomNumber?.toLowerCase().includes(term.toLowerCase())
    )
    setFilteredClasses(filtered)
  }

  const handleAddClass = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log("🚀 handleAddClass called with data:", newClass)
    console.log("📋 Academic years available:", academicYears)
    console.log("📋 Current form state:", {
      name: newClass.name,
      gradeLevel: newClass.gradeLevel,
      academicYearId: newClass.academicYearId,
      academicYearName: newClass.academicYearName
    })

    // Prevent multiple submissions
    if (isSubmitting) {
      console.log("⏳ Form already submitting, ignoring duplicate submission")
      return
    }

    setIsSubmitting(true)

    // Validate required fields
    if (!newClass.name.trim()) {
      console.log("Validation failed: missing class name")
      toast({
        title: "Error",
        description: "Please enter a class name.",
        variant: "destructive",
      })
      return
    }

    if (newClass.name.trim().length < 2) {
      toast({
        title: "Error",
        description: "Class name must be at least 2 characters long.",
        variant: "destructive",
      })
      return
    }

    // Check for duplicate class names
    const duplicateClass = classes.find(cls =>
      cls.name.toLowerCase() === newClass.name.trim().toLowerCase() &&
      cls.gradeLevel === newClass.gradeLevel &&
      cls.section?.toLowerCase() === newClass.section?.trim().toLowerCase()
    )

    if (duplicateClass) {
      toast({
        title: "Error",
        description: "A class with this name, grade level, and section already exists.",
        variant: "destructive",
      })
      return
    }

    if (!newClass.gradeLevel) {
      toast({
        title: "Error",
        description: "Please select a grade level.",
        variant: "destructive",
      })
      return
    }

    if (newClass.capacity && (parseInt(newClass.capacity) < 1 || parseInt(newClass.capacity) > 200)) {
      toast({
        title: "Error",
        description: "Class capacity must be between 1 and 200 students.",
        variant: "destructive",
      })
      return
    }

    // Ensure we have an academic year
    if (!newClass.academicYearName.trim() && !newClass.academicYearId) {
      // Try to use the first available academic year as fallback
      const fallbackYear = academicYears.find(y => y.isActive) || academicYears[0]
      if (fallbackYear) {
        console.log("📅 Using fallback academic year:", fallbackYear.name)
        setNewClass(prev => ({
          ...prev,
          academicYearId: fallbackYear.id,
          academicYearName: fallbackYear.name
        }))
        // Continue with this academic year
      } else {
        toast({
          title: "Error",
          description: "Please enter or select an academic year.",
          variant: "destructive",
        })
        return
      }
    }

    try {
      let academicYearId = newClass.academicYearId
      console.log("📅 Academic year processing - ID:", academicYearId, "Name:", newClass.academicYearName)

      // If user typed a new academic year name, create it first
      if (newClass.academicYearName && !newClass.academicYearId) {
        console.log("🆕 Creating new academic year:", newClass.academicYearName)
        try {
          const currentYear = new Date().getFullYear()
          const academicYearData = {
            name: newClass.academicYearName.trim(),
            startDate: `${currentYear}-09-01`,
            endDate: `${currentYear + 1}-06-30`,
            isActive: true
          }

          const yearResponse = await backendApi.academicYear.createAcademicYear(academicYearData)
          console.log("📅 Academic year creation response:", yearResponse)

          // Handle different response formats
          let yearSuccess = false
          let yearData = null

          if (yearResponse && typeof yearResponse === 'object') {
            if (yearResponse.success === true && yearResponse.data) {
              yearSuccess = true
              yearData = yearResponse.data
            } else if (yearResponse.data && yearResponse.data.success === true) {
              yearSuccess = true
              yearData = yearResponse.data.data
            } else if (yearResponse.status >= 200 && yearResponse.status < 300) {
              yearSuccess = true
              yearData = yearResponse.data
            }
          }

          if (yearSuccess && yearData) {
            academicYearId = yearData.id
            console.log("✅ Created academic year with ID:", academicYearId)
            // Refresh academic years list
            fetchAcademicYears()
          } else {
            console.log("❌ Academic year creation failed, using existing academic year")
            // Don't throw error, just use an existing academic year
            const fallbackYear = academicYears.find(y => y.isActive) || academicYears[0]
            if (fallbackYear) {
              academicYearId = fallbackYear.id
              console.log("📅 Using fallback academic year:", fallbackYear.name, fallbackYear.id)
              toast({
                title: "Warning",
                description: `Could not create new academic year. Using "${fallbackYear.name}" instead.`,
                variant: "destructive",
              })
            } else {
              toast({
                title: "Error",
                description: "No academic year available. Please create an academic year first.",
                variant: "destructive",
              })
              return
            }
          }
        } catch (yearError: any) {
          console.error("❌ Academic year creation error:", yearError)
          // Don't fail the entire form, use fallback
          const fallbackYear = academicYears.find(y => y.isActive) || academicYears[0]
          if (fallbackYear) {
            academicYearId = fallbackYear.id
            console.log("📅 Using fallback academic year due to error:", fallbackYear.name)
            toast({
              title: "Warning",
              description: `Could not create new academic year. Using "${fallbackYear.name}" instead.`,
              variant: "destructive",
            })
          } else {
            toast({
              title: "Error",
              description: "Failed to create academic year and no fallback available.",
              variant: "destructive",
            })
            return
          }
        }
      }

      // Ensure we have an academic year ID
      if (!academicYearId) {
        console.log("❌ No academic year ID found")
        toast({
          title: "Error",
          description: "Academic year is required. Please select or create an academic year.",
          variant: "destructive",
        })
        return
      }

      // Prepare data exactly as backend expects
      const classData = {
        name: newClass.name.trim(),
        gradeLevel: newClass.gradeLevel.trim(),
        section: newClass.section?.trim() || null,
        capacity: newClass.capacity ? parseInt(newClass.capacity) : null,
        roomNumber: newClass.roomNumber?.trim() || null,
        academicYearId: academicYearId,
        subjects: newClass.subjects || []
      }

      console.log("📤 Final class data being sent:", classData)

      console.log("🔄 Creating class with data:", classData)

      let response
      try {
        console.log("🔄 Trying enhanced API client: /api/classes")
        response = await backendApi.class.createClass(classData)
        console.log("📥 Enhanced API response:", response)
      } catch (enhancedError) {
        console.log("❌ Enhanced API failed, trying legacy method:", enhancedError)
        try {
          // Fallback to direct API call
          response = await fetch('/api/classes', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(classData),
          })

          if (response.ok) {
            const data = await response.json()
            response = { success: true, data: data, status: response.status }
          } else {
            const errorData = await response.json()
            response = { success: false, error: errorData.error || 'Request failed', status: response.status }
          }
          console.log("📥 Legacy API response:", response)
        } catch (legacyError) {
          console.error("❌ Both API methods failed:", legacyError)
          throw new Error("Failed to create class - API unavailable")
        }
      }

      // The enhanced API client returns the response in a specific format
      // Check for success in the response data
      let isSuccess = false
      let responseData = null
      let errorMessage = "Failed to create class"

      if (response && typeof response === 'object') {
        // Check if it's the enhanced API client format
        if (response.success !== undefined) {
          isSuccess = response.success === true
          responseData = response.data
          errorMessage = response.error || "Failed to create class"
        }
        // Check if it's a direct backend response format
        else if (response.data && response.data.success !== undefined) {
          isSuccess = response.data.success === true
          responseData = response.data.data
          errorMessage = response.data.error || "Failed to create class"
        }
        // Check for HTTP status codes
        else if (response.status) {
          isSuccess = response.status >= 200 && response.status < 300
          responseData = response.data
          errorMessage = response.error || response.message || "Failed to create class"
        }
        // Fallback: check if we have data
        else if (response.data) {
          isSuccess = true
          responseData = response.data
        }
      }

      console.log("📊 Processed response - Success:", isSuccess, "Data:", responseData, "Error:", errorMessage)

      if (isSuccess) {
        console.log("✅ Class created successfully")
        console.log("📊 Classes state before refresh:", classes.length, "classes")

        toast({
          title: "Success",
          description: `Class "${classData.name}" has been created successfully.`,
        })

        setIsAddClassOpen(false)
        resetForm()

        // Refresh the classes list to show the new class
        console.log("🔄 Refreshing classes list after successful creation...")

        // Add a small delay to ensure backend has processed the new class
        console.log("⏳ Waiting 500ms before refresh...")
        await new Promise(resolve => setTimeout(resolve, 500))

        await fetchClasses()
        console.log("📊 Classes state after refresh:", classes.length, "classes")
      } else {
        console.log("❌ Class creation failed:", errorMessage)
        throw new Error(errorMessage)
      }
    } catch (error: any) {
      console.error("❌ Error creating class:", error)
      console.error("❌ Error stack:", error.stack)

      let errorMessage = "Failed to create class. Please try again."

      if (error.message) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      console.log("🔄 Resetting form submission state")
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    const activeYear = academicYears.find(year => year.isActive)
    setNewClass({
      name: "",
      gradeLevel: "",
      section: "",
      capacity: "",
      roomNumber: "",
      academicYearId: activeYear?.id || "",
      academicYearName: activeYear?.name || "",
      subjects: []
    })
    setIsSubmitting(false)
  }

  const handleEditClass = (classToEdit: Class) => {
    console.log("🔄 Opening edit dialog for class:", classToEdit)
    setEditingClass(classToEdit)
    setNewClass({
      name: classToEdit.name,
      gradeLevel: classToEdit.gradeLevel,
      section: classToEdit.section || "",
      capacity: classToEdit.capacity?.toString() || "",
      roomNumber: classToEdit.roomNumber || "",
      academicYearId: classToEdit.academicYear?.id || "",
      academicYearName: classToEdit.academicYear?.name || "",
      subjects: classToEdit.subjects?.map(s => s.id) || []
    })
    setIsEditClassOpen(true)
  }

  const handleUpdateClass = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!editingClass) {
      toast({
        title: "Error",
        description: "No class selected for editing.",
        variant: "destructive",
      })
      return
    }

    if (isSubmitting) {
      console.log("Form already submitting, ignoring duplicate submission")
      return
    }

    setIsSubmitting(true)

    try {
      // Validate required fields (same as create)
      if (!newClass.name.trim()) {
        toast({
          title: "Error",
          description: "Please enter a class name.",
          variant: "destructive",
        })
        return
      }

      if (!newClass.gradeLevel) {
        toast({
          title: "Error",
          description: "Please select a grade level.",
          variant: "destructive",
        })
        return
      }

      // Check for duplicate class names (excluding current class)
      const duplicateClass = classes.find(cls =>
        cls.id !== editingClass.id &&
        cls.name.toLowerCase() === newClass.name.trim().toLowerCase() &&
        cls.gradeLevel === newClass.gradeLevel &&
        cls.section?.toLowerCase() === newClass.section?.trim().toLowerCase()
      )

      if (duplicateClass) {
        toast({
          title: "Error",
          description: "A class with this name, grade level, and section already exists.",
          variant: "destructive",
        })
        return
      }

      const classData = {
        name: newClass.name.trim(),
        gradeLevel: newClass.gradeLevel.trim(),
        section: newClass.section?.trim() || null,
        capacity: newClass.capacity ? parseInt(newClass.capacity) : null,
        roomNumber: newClass.roomNumber?.trim() || null,
        academicYearId: newClass.academicYearId || editingClass.academicYear?.id,
        subjects: newClass.subjects || []
      }

      console.log("🔄 Updating class with data:", classData)
      const response = await backendApi.class.updateClass(editingClass.id, classData)
      console.log("📥 Class update response:", response)

      const isSuccess = response.success === true || (response.status >= 200 && response.status < 300) || response.data

      if (isSuccess) {
        console.log("✅ Class updated successfully")
        toast({
          title: "Success",
          description: `Class "${classData.name}" has been updated successfully.`,
        })

        setIsEditClassOpen(false)
        setEditingClass(null)
        resetForm()
        await fetchClasses()
      } else {
        console.log("❌ Class update failed:", response)
        const errorMessage = response.error || response.message || "Failed to update class"
        throw new Error(errorMessage)
      }
    } catch (error: any) {
      console.error("Error updating class:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update class. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteClass = async (classToDelete: Class) => {
    const confirmDelete = window.confirm(
      `Are you sure you want to delete the class "${classToDelete.name}"? This action cannot be undone.`
    )

    if (!confirmDelete) {
      return
    }

    try {
      console.log("🔄 Deleting class:", classToDelete.id)
      const response = await backendApi.class.deleteClass(classToDelete.id)
      console.log("📥 Class delete response:", response)

      const isSuccess = response.success === true || (response.status >= 200 && response.status < 300) || response.data

      if (isSuccess) {
        console.log("✅ Class deleted successfully")
        toast({
          title: "Success",
          description: `Class "${classToDelete.name}" has been deleted successfully.`,
        })

        await fetchClasses()
      } else {
        console.log("❌ Class deletion failed:", response)
        const errorMessage = response.error || response.message || "Failed to delete class"
        throw new Error(errorMessage)
      }
    } catch (error: any) {
      console.error("Error deleting class:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete class. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewClass(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    if (name === "academicYearId") {
      const selectedYear = academicYears.find(year => year.id === value)
      setNewClass(prev => ({
        ...prev,
        academicYearId: value,
        academicYearName: selectedYear?.name || ""
      }))
    } else {
      setNewClass(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleAcademicYearNameChange = (value: string) => {
    // Check if the typed value matches an existing academic year
    const existingYear = academicYears.find(year =>
      year.name.toLowerCase() === value.toLowerCase()
    )

    setNewClass(prev => ({
      ...prev,
      academicYearName: value,
      academicYearId: existingYear?.id || ""
    }))
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Classes Management</h2>
          <p className="text-muted-foreground">Manage classes, sections, and assign teachers and students</p>
        </div>
        <div className="flex items-center space-x-2">


          <Button
            variant="outline"
            onClick={async () => {
              console.log("🧪 Testing direct API call...")
              try {
                const response = await fetch('/api/classes', {
                  method: 'GET',
                  credentials: 'include',
                })
                const data = await response.json()
                console.log("🧪 Direct API response:", data)
                console.log("🧪 Direct API response type:", typeof data)
                console.log("🧪 Direct API data length:", data?.data?.length || 'no data')

                toast({
                  title: "Direct API Test",
                  description: `Direct API returned ${data?.data?.length || 0} classes`,
                })
              } catch (error) {
                console.error("🧪 Direct API error:", error)
                toast({
                  title: "Direct API Error",
                  description: "Direct API call failed",
                  variant: "destructive",
                })
              }
            }}
          >
            🧪 Test Direct API
          </Button>
          <Dialog open={isAddClassOpen} onOpenChange={setIsAddClassOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Class
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Add New Class</DialogTitle>
                <DialogDescription>Enter the details of the new class.</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddClass}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="class-name" className="text-right">
                      Class Name *
                    </Label>
                    <Input 
                      id="class-name" 
                      name="name"
                      value={newClass.name}
                      onChange={handleInputChange}
                      className="col-span-3" 
                      placeholder="e.g., Class 1-A"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="grade" className="text-right">
                      Grade Level *
                    </Label>
                    <Select value={newClass.gradeLevel} onValueChange={(value) => handleSelectChange("gradeLevel", value)}>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select grade level" />
                      </SelectTrigger>
                      <SelectContent>
                        {gradeOptions.map((grade) => (
                          <SelectItem key={grade} value={grade}>
                            {grade}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="section" className="text-right">
                      Section
                    </Label>
                    <Input
                      id="section"
                      name="section"
                      value={newClass.section}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="e.g., A, B, C"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="capacity" className="text-right">
                      Capacity
                    </Label>
                    <Input
                      id="capacity"
                      name="capacity"
                      type="number"
                      value={newClass.capacity}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="Maximum number of students"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="room" className="text-right">
                      Room Number
                    </Label>
                    <Input
                      id="room"
                      name="roomNumber"
                      value={newClass.roomNumber}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="e.g., 101, A-205"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="academic-year" className="text-right">
                      Academic Year *
                    </Label>
                    <div className="col-span-3 space-y-2">
                      <Input
                        id="academic-year"
                        name="academicYearName"
                        value={newClass.academicYearName}
                        onChange={(e) => handleAcademicYearNameChange(e.target.value)}
                        placeholder="e.g., 2024-2025, Academic Year 2024"
                        required
                      />
                      {academicYears.length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          Available: {academicYears.map(year => year.name).join(", ")}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsAddClassOpen(false)
                      resetForm()
                    }}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Class"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          {/* Edit Class Dialog */}
          <Dialog open={isEditClassOpen} onOpenChange={setIsEditClassOpen}>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Edit Class</DialogTitle>
                <DialogDescription>Update the details of the class.</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleUpdateClass}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-class-name" className="text-right">
                      Class Name *
                    </Label>
                    <Input
                      id="edit-class-name"
                      name="name"
                      value={newClass.name}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="e.g., Class 1-A"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-grade" className="text-right">
                      Grade Level *
                    </Label>
                    <Select value={newClass.gradeLevel} onValueChange={(value) => handleSelectChange("gradeLevel", value)}>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select grade level" />
                      </SelectTrigger>
                      <SelectContent>
                        {gradeOptions.map((grade) => (
                          <SelectItem key={grade} value={grade}>
                            {grade}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-section" className="text-right">
                      Section
                    </Label>
                    <Input
                      id="edit-section"
                      name="section"
                      value={newClass.section}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="e.g., A, B, C"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-capacity" className="text-right">
                      Capacity
                    </Label>
                    <Input
                      id="edit-capacity"
                      name="capacity"
                      type="number"
                      value={newClass.capacity}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="Maximum number of students"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-room" className="text-right">
                      Room Number
                    </Label>
                    <Input
                      id="edit-room"
                      name="roomNumber"
                      value={newClass.roomNumber}
                      onChange={handleInputChange}
                      className="col-span-3"
                      placeholder="e.g., 101, A-205"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsEditClassOpen(false)
                      setEditingClass(null)
                      resetForm()
                    }}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      "Update Class"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Class Directory</CardTitle>
          <CardDescription>View and manage all classes in your institution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-4">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search classes..."
                className="w-full pl-8"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Class Name</TableHead>
                  <TableHead>Grade Level</TableHead>
                  <TableHead>Section</TableHead>
                  <TableHead>Students</TableHead>
                  <TableHead>Room</TableHead>
                  <TableHead>Academic Year</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                      Loading classes...
                    </TableCell>
                  </TableRow>
                ) : filteredClasses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      No classes found. {searchTerm && "Try adjusting your search terms or "}
                      <Button variant="link" className="p-0 h-auto" onClick={() => setIsAddClassOpen(true)}>
                        create a new class
                      </Button>
                      .
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredClasses.map((cls) => (
                    <TableRow key={cls.id}>
                      <TableCell className="font-medium">{cls.name}</TableCell>
                      <TableCell>{cls.gradeLevel}</TableCell>
                      <TableCell>{cls.section || "-"}</TableCell>
                      <TableCell>{cls.studentEnrollments?.length || 0}</TableCell>
                      <TableCell>{cls.roomNumber || "-"}</TableCell>
                      <TableCell>{cls.academicYear?.name || "-"}</TableCell>
                      <TableCell>
                        <Badge variant={cls.isActive ? "default" : "secondary"}>
                          {cls.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditClass(cls)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Users className="mr-2 h-4 w-4" />
                              Manage Students
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDeleteClass(cls)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function ClassesPage() {
  return <ClassesContent />
}
