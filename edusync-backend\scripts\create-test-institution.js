const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestInstitution() {
  try {
    // Create a test institution
    const institution = await prisma.institution.create({
      data: {
        id: "test-institution-123", // Use the same ID as in the frontend
        name: "Test Institution",
        email: "<EMAIL>",
        logo: null,
        subscriptionStatus: "TRIAL",
        subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isActive: true,
        address: "123 Education Street",
        city: "Learning City",
        state: "Knowledge State",
        country: "Education Country",
        phoneNumber: "+****************",
        website: "https://test.edu"
      }
    });
    
    console.log('Created test institution:', institution);
    console.log('Institution ID:', institution.id);
    
    // Create a test user and associate with the institution
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.iK8i', // password: TestUser123!
        firstName: 'Test',
        lastName: 'User',
        role: 'INSTITUTION_ADMIN',
        isEmailVerified: true,
        isActive: true
      }
    });
    
    console.log('Created test user:', user.email);
    
    // Associate user with institution
    const institutionUser = await prisma.institutionUser.create({
      data: {
        userId: user.id,
        institutionId: institution.id,
        role: 'INSTITUTION_ADMIN'
      }
    });
    
    console.log('Associated user with institution:', institutionUser);
    console.log('Test setup complete!');
    console.log('You can now login with: <EMAIL> / TestUser123!');
    
  } catch (error) {
    console.error('Error creating test institution:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestInstitution(); 