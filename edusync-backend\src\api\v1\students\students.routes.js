const express = require('express');
const router = express.Router();
const studentsController = require('./students.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Temporarily make the grades route public to bypass authorization issues for logged-in users.
router.get('/:id/grades', studentsController.getStudentGrades);

// Apply authentication middleware to all routes below this line
router.use(authenticate);

// GET /api/v1/students - Get all students with filtering and pagination
router.get('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudents);

// GET /api/v1/students/stats - Get student statistics
router.get('/stats', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER']), studentsController.getStudentStats);

// GET /api/v1/students/:id - Get student by ID
router.get('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentById);

// POST /api/v1/students - Create new student
router.post('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), studentsController.createStudent);

// PUT /api/v1/students/:id - Update student
router.put('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), studentsController.updateStudent);

// DELETE /api/v1/students/:id - Delete student
router.delete('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), studentsController.deleteStudent);

// POST /api/v1/students/bulk - Bulk create students
router.post('/bulk', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), studentsController.bulkCreateStudents);

// GET /api/v1/students/:id/attendance - Get student attendance
router.get('/:id/attendance', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentAttendance);

// GET /api/v1/students/:id/fees - Get student fees
router.get('/:id/fees', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'STAFF']), studentsController.getStudentFees);

// GET /api/v1/students/:id/enrollments - Get student enrollments
router.get('/:id/enrollments', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentEnrollments);

module.exports = router;