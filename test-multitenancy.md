# Multitenancy Testing Guide

## Summary of Fixes Applied

I've implemented comprehensive school-based filtering across all major controllers to ensure proper multitenancy:

### Controllers Fixed:
1. **Students Controller** (`/api/v1/students`) - Now filters students by school context
2. **Classes Controller** (`/api/v1/classes`) - Now filters classes by school context  
3. **Subjects Controller** (`/api/v1/subjects`) - Enhanced school filtering with access validation
4. **Academic Years Controller** (`/api/v1/academic-years`) - Added school-based filtering
5. **Exams Controller** (`/api/v1/exams`) - Enhanced with school access validation

### Key Security Improvements:

#### For School Admin Users:
- **Strict School Isolation**: Can only access data from their assigned school (`req.user.schoolId`)
- **Query Parameter Validation**: If they try to access a different school via query params, access is denied
- **Automatic Filtering**: All queries are automatically filtered by their school ID

#### For Institution Admin Users:
- **Institution-wide Access**: Can access data from all schools within their institution
- **School Validation**: If requesting specific school data, validates the school belongs to their institution

#### For Super Admin Users:
- **Full Access**: Can access all data across all schools and institutions

## Testing Steps

### 1. Test School Admin Isolation

**Login as School Admin and verify:**

```bash
# Test students endpoint
GET /api/v1/students
# Should only return students from the school admin's school

# Test classes endpoint  
GET /api/v1/classes
# Should only return classes from the school admin's school

# Test subjects endpoint
GET /api/v1/subjects
# Should only return subjects from the school admin's school

# Test academic years endpoint
GET /api/v1/academic-years
# Should only return academic years from the school admin's school

# Test exams endpoint
GET /api/v1/exams
# Should only return exams from the school admin's school
```

**Test access denial:**
```bash
# Try to access different school's data (should fail)
GET /api/v1/students?schoolId=different-school-id
GET /api/v1/subjects?schoolId=different-school-id
GET /api/v1/exams?schoolId=different-school-id
# All should return 403 Forbidden
```

### 2. Test Institution Admin Access

**Login as Institution Admin and verify:**

```bash
# Should see data from all schools in their institution
GET /api/v1/students
GET /api/v1/classes
GET /api/v1/subjects
GET /api/v1/academic-years
GET /api/v1/exams

# Should be able to filter by specific school in their institution
GET /api/v1/students?schoolId=school-in-their-institution
GET /api/v1/classes?schoolId=school-in-their-institution

# Should be denied access to schools outside their institution
GET /api/v1/students?schoolId=school-in-different-institution
# Should return 403 Forbidden
```

### 3. Verify Console Logs

Check the backend console for these log messages that confirm filtering is working:

```
🔍 Getting students for user: [user-id] role: SCHOOL_ADMIN
🔍 User schoolId: [school-id]
🔍 School admin - filtering by schoolId: [school-id]
🔍 Final where clause: {"studentStatus":"ACTIVE","enrollments":{"some":{"schoolId":"[school-id]","isActive":true}}}
```

### 4. Database Verification

You can also verify in the database that:
1. School admin users have a `schoolId` in their user record or school associations
2. The queries are actually filtering by the correct school ID
3. No cross-school data is being returned

## Expected Behavior

### Before Fix:
- School admins could see students/classes/subjects from ALL schools
- No filtering based on user's school association
- Major security vulnerability allowing data leakage

### After Fix:
- School admins can ONLY see data from their assigned school
- Institution admins can only see data from schools in their institution
- Automatic query parameter validation prevents unauthorized access
- Comprehensive logging for audit trails

## Troubleshooting

If you're still seeing cross-school data:

1. **Check User School Association**: Verify the logged-in user has `schoolId` set in `req.user.schoolId`
2. **Check Console Logs**: Look for the filtering log messages to confirm the where clauses are being applied
3. **Verify Authentication**: Ensure the authentication middleware is properly setting the school context
4. **Test with Different Users**: Try with users from different schools to confirm isolation

The key fix is that now ALL data queries are automatically filtered by the user's school context, preventing any data leakage between schools.
