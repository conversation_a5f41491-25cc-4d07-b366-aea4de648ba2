import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtVerify } from 'jose'

// Define role-based routes
const roleBasedRoutes = {
  SUPER_ADMIN: '/super-admin',
  INSTITUTION_ADMIN: '/institution-admin',
  SCHOOL_ADMIN: '/school-admin',
  TEACHER: '/teacher',
  STUDENT: '/student',
  PARENT: '/parent',
  STAFF: '/staff',
}

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/super-admin',
  '/onboarding',
]

async function verifyToken(token: string) {
  try {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET)
    const { payload } = await jwtVerify(token, secret)
    return payload
  } catch (error) {
    return null
  }
}

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function middleware(request: NextRequest) {
  const sessionToken = request.cookies.get('session')?.value
  const accessToken = request.cookies.get('accessToken')?.value
  const path = request.nextUrl.pathname

  // Allow access to home page for non-logged-in users
  if (path === '/' && !sessionToken && !accessToken) {
    return NextResponse.next()
  }

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route))

  // If trying to access protected routes without any tokens
  if (isProtectedRoute && !sessionToken && !accessToken) {
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }

  // If we have either token, verify session with backend before redirecting
  if (sessionToken || accessToken) {
    // Try to verify session with backend
    let backendValid = false
    let userRole: string | undefined = undefined
    try {
      const backendResponse = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken || sessionToken}`,
          "Content-Type": "application/json",
        },
      })
      if (backendResponse.ok) {
        const backendData = await backendResponse.json()
        userRole = backendData.data?.user?.role || backendData.user?.role
        backendValid = true
      }
    } catch (e) {
      // Network or backend error, treat as invalid
      backendValid = false
    }

    if (!backendValid) {
      // Session is invalid, clear cookies and redirect to login
      const response = NextResponse.redirect(new URL('/auth/login', request.url))
      response.cookies.set('session', '', { maxAge: 0 })
      response.cookies.set('accessToken', '', { maxAge: 0 })
      return response
    }

    // If user is logged in and trying to access home page or auth pages
    if ((path === '/' || path.startsWith('/auth')) && userRole) {
      const role = userRole as keyof typeof roleBasedRoutes
      const redirectUrl = roleBasedRoutes[role] || '/dashboard'
      return NextResponse.redirect(new URL(redirectUrl, request.url))
    }

    // Handle role-specific access control
    if (userRole) {
      const role = userRole as keyof typeof roleBasedRoutes
      // Super admins can only access super-admin routes
      if (role === 'SUPER_ADMIN' && !path.startsWith('/super-admin')) {
        return NextResponse.redirect(new URL('/super-admin', request.url))
      }
      // Non-super-admin users cannot access super-admin routes
      if (role !== 'SUPER_ADMIN' && path.startsWith('/super-admin')) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    } else if (isProtectedRoute) {
      // If we can't verify the session and it's a protected route, redirect to login
      const response = NextResponse.redirect(new URL('/auth/login', request.url))
      response.cookies.set('session', '', { maxAge: 0 })
      response.cookies.set('accessToken', '', { maxAge: 0 })
      return response
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}