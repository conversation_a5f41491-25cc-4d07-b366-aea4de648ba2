const axios = require('axios');

const BACKEND_URL = 'http://localhost:4000';

async function testBackend() {
  try {
    console.log('🔍 Testing backend connectivity...');
    
    // Test if backend is running
    const healthResponse = await axios.get(`${BACKEND_URL}/health`);
    console.log('✅ Backend health check:', healthResponse.status);
    
    // Test login
    const loginResponse = await axios.post(`${BACKEND_URL}/api/v1/auth/login`, {
      email: '<EMAIL>',
      password: 'InstAdmin123!'
    });
    
    console.log('✅ Login successful');
    const { accessToken } = loginResponse.data.data;
    
    // Test students endpoint
    const studentsResponse = await axios.get(`${BACKEND_URL}/api/v1/students`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    
    console.log('✅ Students endpoint working');
    console.log('Students found:', studentsResponse.data.data.students.length);
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

testBackend(); 