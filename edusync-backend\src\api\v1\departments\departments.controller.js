const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Get all departments
const getAllDepartments = async (req, res) => {
  try {
    console.log('🔍 Getting departments for user:', req.user.id, 'role:', req.user.role);

    // Get user's institution(s) - for multi-tenant support
    let institutionIds = [];

    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can access all departments
      institutionIds = null;
      console.log('  - Super admin: accessing all departments');
    } else {
      // Get user's institutions from InstitutionUser relationship
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      console.log('  - User institutions found:', userInstitutions.length);

      if (userInstitutions.length > 0) {
        institutionIds = userInstitutions.map(ui => ui.institutionId);
        console.log('  - Institution IDs:', institutionIds);
      } else {
        // Handle case where user has no institution association
        console.log('WARNING: User has no institution associations');
        return res.status(403).json({
          success: false,
          message: 'User has no institution associations. Please contact your administrator.'
        });
      }
    }

    const where = {
      AND: [
        { isActive: true }
      ]
    };

    // Filter by user's institutions
    if (institutionIds) {
      where.AND.push({
        school: {
          institutionId: { in: institutionIds }
        }
      });
    }

    const departments = await prisma.department.findMany({
      where,
      include: {
        school: {
          select: {
            id: true,
            name: true,
            institutionId: true
          }
        },
        _count: {
          select: {
            subjects: true,
            teachers: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.json({
      success: true,
      data: departments,
      total: departments.length
    });
  } catch (error) {
    console.error('Error fetching departments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch departments'
    });
  }
};

// Get department by ID
const getDepartmentById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get user's institution(s) - for multi-tenant support
    let institutionIds = [];

    if (req.user.role === 'SUPER_ADMIN') {
      institutionIds = null;
    } else {
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      if (userInstitutions.length > 0) {
        institutionIds = userInstitutions.map(ui => ui.institutionId);
      } else {
        institutionIds = null;
      }
    }

    const where = {
      id,
      isActive: true
    };

    if (institutionIds) {
      where.school = {
        institutionId: { in: institutionIds }
      };
    }

    const department = await prisma.department.findFirst({
      where,
      include: {
        school: {
          select: {
            id: true,
            name: true,
            institutionId: true
          }
        },
        subjects: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            code: true,
            isCore: true
          }
        },
        teachers: {
          where: { isActive: true },
          include: {
            teacher: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!department) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }

    res.json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('Error fetching department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch department'
    });
  }
};

// Create new department
const createDepartment = async (req, res) => {
  try {
    console.log('🔍 Creating department for user:', req.user.id, 'role:', req.user.role);
    console.log('  - Request body:', req.body);

    const { name, description, schoolId } = req.body;

    // Validate required fields
    if (!name) {
      console.log('❌ Department name is missing');
      return res.status(400).json({
        success: false,
        message: 'Department name is required'
      });
    }

    // Get user's school information if schoolId not provided
    let finalSchoolId = schoolId;

    if (!finalSchoolId) {
      console.log('  - No schoolId provided, determining from user context');
      console.log('  - User role:', req.user.role);

      // Get user's institutions first
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      console.log('  - User institutions:', userInstitutions.length);

      if (userInstitutions.length > 0) {
        // Get the first school from user's institutions
        const firstSchool = await prisma.school.findFirst({
          where: {
            institutionId: { in: userInstitutions.map(ui => ui.institutionId) }
          },
          select: { id: true }
        });

        console.log('  - First school found:', !!firstSchool);

        if (firstSchool) {
          finalSchoolId = firstSchool.id;
          console.log('  - Using school ID:', finalSchoolId);
        }
      }
    }

    if (!finalSchoolId) {
      console.log('❌ No school ID could be determined');
      return res.status(400).json({
        success: false,
        message: 'School ID is required and could not be determined from user context'
      });
    }

    // Check if department with same name already exists in the school
    const existingDepartment = await prisma.department.findFirst({
      where: {
        name,
        schoolId: finalSchoolId,
        isActive: true
      }
    });

    if (existingDepartment) {
      return res.status(400).json({
        success: false,
        message: 'Department with this name already exists in the school'
      });
    }

    const department = await prisma.department.create({
      data: {
        name,
        description,
        schoolId: finalSchoolId
      },
      include: {
        school: {
          select: {
            id: true,
            name: true,
            institutionId: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: department,
      message: 'Department created successfully'
    });
  } catch (error) {
    console.error('Error creating department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create department'
    });
  }
};

// Update department
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // Check if department exists and user has access
    const existingDepartment = await prisma.department.findFirst({
      where: {
        id,
        isActive: true
      },
      include: {
        school: true
      }
    });

    if (!existingDepartment) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }

    // Check permissions
    if (req.user.role !== 'SUPER_ADMIN') {
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      const institutionIds = userInstitutions.map(ui => ui.institutionId);

      if (institutionIds.length > 0 && !institutionIds.includes(existingDepartment.school.institutionId)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: Cannot update this department'
        });
      }
    }

    const department = await prisma.department.update({
      where: { id },
      data: {
        name,
        description
      },
      include: {
        school: {
          select: {
            id: true,
            name: true,
            institutionId: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: department,
      message: 'Department updated successfully'
    });
  } catch (error) {
    console.error('Error updating department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update department'
    });
  }
};

// Delete department (soft delete)
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if department exists and user has access
    const existingDepartment = await prisma.department.findFirst({
      where: {
        id,
        isActive: true
      },
      include: {
        school: true,
        _count: {
          select: {
            subjects: true,
            teachers: true
          }
        }
      }
    });

    if (!existingDepartment) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }

    // Check if department has active subjects or teachers
    if (existingDepartment._count.subjects > 0 || existingDepartment._count.teachers > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete department with active subjects or teachers'
      });
    }

    // Check permissions
    if (req.user.role !== 'SUPER_ADMIN') {
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      const institutionIds = userInstitutions.map(ui => ui.institutionId);

      if (institutionIds.length > 0 && !institutionIds.includes(existingDepartment.school.institutionId)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: Cannot delete this department'
        });
      }
    }

    await prisma.department.update({
      where: { id },
      data: { isActive: false }
    });

    res.json({
      success: true,
      message: 'Department deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete department'
    });
  }
};

module.exports = {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment
};
