import { NextRequest, NextResponse } from "next/server"
import { getValidAuthToken } from "@/lib/auth-utils"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Classes API: Starting GET request...")
    const token = await getValidAuthToken(request)

    if (!token) {
      console.log("❌ Classes API: No valid token, returning 401")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    console.log("✅ Classes API: Token validated successfully")

    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()
    console.log("🔍 Classes API: Query string:", queryString)

    console.log("🔍 Classes API: Making request to backend...")
    const response = await fetch(`${BACKEND_URL}/api/v1/classes?${queryString}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    console.log("🔍 Classes API: Backend response status:", response.status)
    const data = await response.json()
    console.log("🔍 Classes API: Backend response data:", data)

    if (!response.ok) {
      console.log("❌ Classes API: Backend returned error:", response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    console.log("✅ Classes API: Returning successful response")
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Classes API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Classes API POST: Starting create class request...")
    const token = await getValidAuthToken(request)

    if (!token) {
      console.log("❌ Classes API POST: No valid token, returning 401")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    console.log("✅ Classes API POST: Token validated successfully")

    const body = await request.json()
    console.log("🔍 Classes API POST: Request body:", body)

    console.log("🔍 Classes API POST: Making request to backend...")
    const response = await fetch(`${BACKEND_URL}/api/v1/classes`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    console.log("🔍 Classes API POST: Backend response status:", response.status)
    const data = await response.json()
    console.log("🔍 Classes API POST: Backend response data:", data)

    if (!response.ok) {
      console.log("❌ Classes API POST: Backend returned error:", response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    console.log("✅ Classes API POST: Returning successful response")
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Classes API POST error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    console.log("🔍 Classes API PUT: Starting update class request...")
    const token = await getValidAuthToken(request)

    if (!token) {
      console.log("❌ Classes API PUT: No valid token, returning 401")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    console.log("✅ Classes API PUT: Token validated successfully")

    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('id')

    if (!classId) {
      console.log("❌ Classes API PUT: No class ID provided")
      return NextResponse.json({ error: "Class ID is required" }, { status: 400 })
    }

    console.log("🔍 Classes API PUT: Request body:", body)
    console.log("🔍 Classes API PUT: Class ID:", classId)

    console.log("🔍 Classes API PUT: Making request to backend...")
    const response = await fetch(`${BACKEND_URL}/api/v1/classes/${classId}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    console.log("🔍 Classes API PUT: Backend response status:", response.status)
    const data = await response.json()
    console.log("🔍 Classes API PUT: Backend response data:", data)

    if (!response.ok) {
      console.log("❌ Classes API PUT: Backend returned error:", response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    console.log("✅ Classes API PUT: Returning successful response")
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Classes API PUT error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log("🔍 Classes API DELETE: Starting delete class request...")
    const token = await getValidAuthToken(request)

    if (!token) {
      console.log("❌ Classes API DELETE: No valid token, returning 401")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    console.log("✅ Classes API DELETE: Token validated successfully")

    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('id')

    if (!classId) {
      console.log("❌ Classes API DELETE: No class ID provided")
      return NextResponse.json({ error: "Class ID is required" }, { status: 400 })
    }

    console.log("🔍 Classes API DELETE: Class ID:", classId)

    console.log("🔍 Classes API DELETE: Making request to backend...")
    const response = await fetch(`${BACKEND_URL}/api/v1/classes/${classId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    console.log("🔍 Classes API DELETE: Backend response status:", response.status)
    const data = await response.json()
    console.log("🔍 Classes API DELETE: Backend response data:", data)

    if (!response.ok) {
      console.log("❌ Classes API DELETE: Backend returned error:", response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    console.log("✅ Classes API DELETE: Returning successful response")
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Classes API DELETE error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
