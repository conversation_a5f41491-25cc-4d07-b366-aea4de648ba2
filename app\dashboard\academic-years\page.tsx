"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { PlusCircle, Calendar, CheckCircle, Circle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { backendApi } from "@/lib/backend-api"

interface AcademicYear {
  id: string
  name: string
  startDate: string
  endDate: string
  isActive: boolean
  school: {
    id: string
    name: string
  }
  _count: {
    classes: number
  }
}

export default function AcademicYearsPage() {
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [newAcademicYear, setNewAcademicYear] = useState({
    name: "",
    startDate: "",
    endDate: "",
    isActive: false
  })
  const { toast } = useToast()

  useEffect(() => {
    fetchAcademicYears()
  }, [])

  const fetchAcademicYears = async () => {
    try {
      setIsLoading(true)
      const response = await backendApi.academicYear.getAcademicYears()
      
      if (response && response.success) {
        setAcademicYears(response.data || [])
      } else {
        console.error("Failed to fetch academic years:", response?.error)
        setAcademicYears([])
      }
    } catch (error) {
      console.error("Error fetching academic years:", error)
      setAcademicYears([])
      toast({
        title: "Error",
        description: "Failed to load academic years",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddAcademicYear = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isSubmitting) return
    setIsSubmitting(true)

    try {
      // Validate required fields
      if (!newAcademicYear.name.trim()) {
        toast({
          title: "Error",
          description: "Please enter an academic year name",
          variant: "destructive"
        })
        return
      }

      if (!newAcademicYear.startDate || !newAcademicYear.endDate) {
        toast({
          title: "Error", 
          description: "Please select start and end dates",
          variant: "destructive"
        })
        return
      }

      // Validate date range
      const startDate = new Date(newAcademicYear.startDate)
      const endDate = new Date(newAcademicYear.endDate)
      
      if (endDate <= startDate) {
        toast({
          title: "Error",
          description: "End date must be after start date",
          variant: "destructive"
        })
        return
      }

      const response = await backendApi.academicYear.createAcademicYear(newAcademicYear)
      
      if (response && response.success) {
        toast({
          title: "Success",
          description: `Academic year "${newAcademicYear.name}" created successfully`
        })
        
        setIsAddDialogOpen(false)
        resetForm()
        await fetchAcademicYears()
      } else {
        throw new Error(response?.message || "Failed to create academic year")
      }
    } catch (error: any) {
      console.error("Error creating academic year:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to create academic year",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSetActive = async (academicYearId: string) => {
    try {
      const response = await backendApi.academicYear.setActiveAcademicYear(academicYearId)
      
      if (response && response.success) {
        toast({
          title: "Success",
          description: "Academic year set as active"
        })
        await fetchAcademicYears()
      } else {
        throw new Error(response?.message || "Failed to set active academic year")
      }
    } catch (error: any) {
      console.error("Error setting active academic year:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to set active academic year",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setNewAcademicYear({
      name: "",
      startDate: "",
      endDate: "",
      isActive: false
    })
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setNewAcademicYear(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Academic Years</h2>
          <p className="text-muted-foreground">Manage academic years for your school</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Academic Year
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Academic Year</DialogTitle>
              <DialogDescription>Create a new academic year for your school</DialogDescription>
            </DialogHeader>
            <form onSubmit={handleAddAcademicYear}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={newAcademicYear.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="e.g., 2024-2025"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="startDate" className="text-right">Start Date *</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={newAcademicYear.startDate}
                    onChange={handleInputChange}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="endDate" className="text-right">End Date *</Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={newAcademicYear.endDate}
                    onChange={handleInputChange}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="isActive" className="text-right">Set as Active</Label>
                  <div className="col-span-3">
                    <input
                      id="isActive"
                      name="isActive"
                      type="checkbox"
                      checked={newAcademicYear.isActive}
                      onChange={handleInputChange}
                      className="mr-2"
                    />
                    <span className="text-sm text-muted-foreground">
                      This will deactivate other academic years
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsAddDialogOpen(false)
                    resetForm()
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Academic Year"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Academic Years
          </CardTitle>
          <CardDescription>
            Manage academic years and set the active year for your school
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading academic years...</div>
          ) : academicYears.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No academic years found. Create your first academic year to get started.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Classes</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {academicYears.map((year) => (
                  <TableRow key={year.id}>
                    <TableCell className="font-medium">{year.name}</TableCell>
                    <TableCell>{new Date(year.startDate).toLocaleDateString()}</TableCell>
                    <TableCell>{new Date(year.endDate).toLocaleDateString()}</TableCell>
                    <TableCell>{year._count.classes} classes</TableCell>
                    <TableCell>
                      {year.isActive ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <Circle className="mr-1 h-3 w-3" />
                          Inactive
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {!year.isActive && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSetActive(year.id)}
                        >
                          Set Active
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
