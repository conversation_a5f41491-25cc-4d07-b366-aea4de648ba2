const express = require('express');
const { authenticate } = require('../../../middleware/authenticate');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// Get all classes for a school
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('🔍 Backend Classes GET: Request received from user:', req.user?.id, 'role:', req.user?.role);
    console.log('🔍 Backend Classes GET: User schoolId:', req.user?.schoolId);
    console.log('🔍 Backend Classes GET: User institutionId:', req.user?.institutionId);

    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build where clause with school filtering for multitenancy
    let where = {
      isActive: true
    };

    // Apply school-based filtering based on user role and context
    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can see all classes
      console.log('🔍 Backend Classes GET: Super admin - showing all classes');
    } else if (req.user.role === 'SCHOOL_ADMIN' && req.user.schoolId) {
      // School admin can only see classes from their school
      where.schoolId = req.user.schoolId;
      console.log('🔍 Backend Classes GET: School admin - filtering by schoolId:', req.user.schoolId);
    } else if (req.user.institutionId) {
      // Institution admin can see classes from all schools in their institution
      where.school = {
        institutionId: req.user.institutionId
      };
      console.log('🔍 Backend Classes GET: Institution admin - filtering by institutionId:', req.user.institutionId);
    } else {
      // If no school/institution context, deny access
      console.log('❌ Backend Classes GET: No school/institution context found for user');
      return res.status(403).json({
        success: false,
        error: 'Access denied: No school or institution association found'
      });
    }

    console.log('🔍 Backend Classes GET: Final where clause:', JSON.stringify(where, null, 2));

    // Get classes from database
    const [classes, totalCount] = await Promise.all([
      prisma.class.findMany({
        skip,
        take: limit,
        where,
        include: {
          school: {
            select: {
              id: true,
              name: true
            }
          },
          academicYear: {
            select: {
              id: true,
              name: true,
              isActive: true
            }
          },
          _count: {
            select: {
              studentEnrollments: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.class.count({ where })
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    console.log(`✅ Backend Classes GET: Found ${classes.length} classes (page ${page}/${totalPages})`);
    console.log('✅ Backend Classes GET: Sample classes:', classes.slice(0, 2).map(c => ({ id: c.id, name: c.name, school: c.school?.name })));

    const responseData = {
      success: true,
      data: {
        classes,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    };

    console.log('✅ Backend Classes GET: Sending response with', classes.length, 'classes');
    res.status(200).json(responseData);

  } catch (error) {
    console.error('❌ Backend Classes GET: Error fetching classes:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch classes. Please try again.'
    });
  }
});

// Create a new class
router.post('/', authenticate, async (req, res) => {
  try {
    console.log('🔍 Backend Classes POST: Request received from user:', req.user?.id, 'role:', req.user?.role);
    console.log('🔍 Backend Classes POST: Request body:', req.body);

    const {
      name,
      gradeLevel,
      section,
      capacity,
      roomNumber,
      schoolId,
      academicYearId,
      subjects = []
    } = req.body;

    // Validate required fields
    if (!name || !gradeLevel || !academicYearId) {
      console.log('❌ Backend Classes POST: Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Name, grade level, and academic year are required'
      });
    }

    // Determine school ID based on user context and multitenancy
    let finalSchoolId = schoolId;

    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can create classes in any school, but schoolId must be provided
      if (!finalSchoolId) {
        return res.status(400).json({
          success: false,
          error: 'Super admin must specify a school ID when creating classes'
        });
      }
    } else if (req.user.role === 'SCHOOL_ADMIN' && req.user.schoolId) {
      // School admin can only create classes in their school
      if (schoolId && schoolId !== req.user.schoolId) {
        console.log('❌ School admin attempted to create class in different school');
        return res.status(403).json({
          success: false,
          error: 'Access denied: You can only create classes in your school'
        });
      }
      finalSchoolId = req.user.schoolId;
      console.log('🔍 Backend Classes POST: Using school admin\'s school ID:', finalSchoolId);
    } else if (req.user.institutionId) {
      // Institution admin can create classes in schools within their institution
      if (!finalSchoolId) {
        return res.status(400).json({
          success: false,
          error: 'Institution admin must specify a school ID when creating classes'
        });
      }

      // Verify the school belongs to the user's institution
      const school = await prisma.school.findFirst({
        where: {
          id: finalSchoolId,
          institutionId: req.user.institutionId,
          isActive: true
        }
      });

      if (!school) {
        return res.status(403).json({
          success: false,
          error: 'Access denied: School not found in your institution'
        });
      }
    } else {
      // No school/institution context
      return res.status(403).json({
        success: false,
        error: 'Access denied: No school or institution association found'
      });
    }

    console.log('🔍 Backend Classes POST: Final school ID:', finalSchoolId);

    // Create the class
    const newClass = await prisma.class.create({
      data: {
        name: name.trim(),
        gradeLevel: gradeLevel.trim(),
        section: section?.trim() || null,
        capacity: capacity ? parseInt(capacity) : null,
        roomNumber: roomNumber?.trim() || null,
        schoolId: finalSchoolId,
        academicYearId: academicYearId,
        isActive: true
      },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            isActive: true
          }
        }
      }
    });

    console.log('✅ Backend Classes POST: Class created successfully:', newClass.id);

    res.status(201).json({
      success: true,
      data: newClass,
      message: 'Class created successfully'
    });

  } catch (error) {
    console.error('❌ Backend Classes POST: Error creating class:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        error: 'A class with this name already exists in the selected academic year'
      });
    }

    if (error.code === 'P2003') {
      return res.status(400).json({
        success: false,
        error: 'Invalid school ID or academic year ID provided'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create class. Please try again.'
    });
  }
});

module.exports = router;
