import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHeaders } from "@/lib/auth-utils"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)

    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params

    const response = await fetch(`${BACKEND_URL}/api/v1/subjects/${id}/assignments`, {
      method: "GET",
      headers: await createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to fetch subject assignments" },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Subject assignments API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}
