const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkSchool() {
  try {
    const school = await prisma.school.findUnique({
      where: { 
        id: 'cea77542-38ce-4a26-951c-3b54563c5d6c'
      },
      include: {
        institution: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log('School:', school);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSchool(); 