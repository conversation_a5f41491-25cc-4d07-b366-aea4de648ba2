import { NextRequest, NextResponse } from "next/server"
import { getValidAuthToken } from "@/lib/auth-utils"
import { cookies } from "next/headers"
import { verify } from "jsonwebtoken"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  console.log("🔍 Session API: Starting authentication check...")

  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get("session")?.value
    const accessToken = cookieStore.get("accessToken")?.value
    const refreshToken = cookieStore.get("refreshToken")?.value

    console.log("🔍 Session API: Token availability:", {
      sessionToken: !!sessionToken,
      accessToken: !!accessToken,
      refreshToken: !!refreshToken
    })

    // First try to use session token (local JWT) for quick validation
    if (sessionToken) {
      try {
        console.log("🔍 Session API: Verifying session token...")
        const decoded = verify(sessionToken, process.env.JWT_SECRET!) as any
        console.log("✅ Session API: Session token valid for user:", decoded.email)

        // Also verify with backend to ensure access token is still valid
        const token = await getValidAuthToken(request)
        if (token) {
          console.log("🔍 Session API: Validating with backend...")
          const backendResponse = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            signal: AbortSignal.timeout(10000), // Increased timeout
          })

          if (backendResponse.ok) {
            const backendData = await backendResponse.json()
            const user = backendData.data?.user || backendData.user
            console.log("✅ Session API: Backend validation successful")

            return NextResponse.json({
              authenticated: true,
              user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                isEmailVerified: user.isEmailVerified,
                institutions: user.institutions || [],
                schools: user.schools || []
              },
              sessionValid: true,
              tokenStatus: "valid"
            })
          } else {
            console.warn("⚠️ Session API: Backend validation failed:", backendResponse.status)
          }
        } else {
          console.warn("⚠️ Session API: No valid access token available")
        }

        // If backend validation fails but session token is valid,
        // return session data but mark as needing refresh
        console.log("⚠️ Session API: Using session token data with warning")
        return NextResponse.json({
          authenticated: true,
          user: {
            id: decoded.id,
            email: decoded.email,
            firstName: decoded.firstName || "",
            lastName: decoded.lastName || "",
            role: decoded.role,
            isEmailVerified: decoded.isEmailVerified || false,
          },
          sessionValid: false,
          tokenStatus: "needs_refresh",
          warning: "Session token valid but backend validation failed"
        })
      } catch (jwtError) {
        console.error("❌ Session API: Session token verification failed:", jwtError)
        // Clear invalid session token
        cookieStore.delete("session")
        // Continue to try access token only
      }
    }

    // Fallback to access token validation only
    console.log("🔍 Session API: Falling back to access token validation...")
    const token = await getValidAuthToken(request)

    if (!token) {
      console.log("❌ Session API: No valid authentication token found")

      // If we have a refresh token, suggest refresh
      if (refreshToken) {
        return NextResponse.json({
          authenticated: false,
          user: null,
          sessionValid: false,
          tokenStatus: "expired",
          error: "Access token expired",
          canRefresh: true
        }, { status: 401 })
      }

      return NextResponse.json({
        authenticated: false,
        user: null,
        sessionValid: false,
        tokenStatus: "missing",
        error: "No valid authentication token",
        canRefresh: false
      }, { status: 401 })
    }

    // Verify with backend
    console.log("🔍 Session API: Verifying access token with backend...")
    const response = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      signal: AbortSignal.timeout(10000),
    })

    if (!response.ok) {
      console.error("❌ Session API: Backend authentication failed:", response.status)

      // If token is expired and we have refresh token, suggest refresh
      if (response.status === 401 && refreshToken) {
        return NextResponse.json({
          authenticated: false,
          user: null,
          sessionValid: false,
          tokenStatus: "expired",
          error: "Access token expired",
          canRefresh: true
        }, { status: 401 })
      }

      return NextResponse.json({
        authenticated: false,
        user: null,
        sessionValid: false,
        tokenStatus: "invalid",
        error: "Backend authentication failed",
        canRefresh: !!refreshToken
      }, { status: 401 })
    }

    const data = await response.json()
    const user = data.data?.user || data.user

    if (!user) {
      console.error("❌ Session API: User data not found in backend response")
      return NextResponse.json({
        authenticated: false,
        user: null,
        sessionValid: false,
        tokenStatus: "invalid",
        error: "User data not found",
        canRefresh: !!refreshToken
      }, { status: 401 })
    }

    console.log("✅ Session API: Access token validation successful")
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        institutions: user.institutions || [],
        schools: user.schools || []
      },
      sessionValid: true,
      tokenStatus: "valid"
    })
  } catch (error) {
    console.error("❌ Session API: Unexpected error:", error)

    // Provide more specific error information
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    const isNetworkError = errorMessage.includes("fetch") || errorMessage.includes("timeout")

    return NextResponse.json({
      authenticated: false,
      user: null,
      sessionValid: false,
      tokenStatus: "error",
      error: isNetworkError ? "Network error during authentication" : "Session check failed",
      canRefresh: false,
      details: process.env.NODE_ENV === "development" ? errorMessage : undefined
    }, { status: 500 })
  }
}