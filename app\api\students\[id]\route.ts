import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log('🔍 Student GET API: Fetching student with ID:', params.id)
    
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      console.log('❌ Student GET API: No access token found')
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log('🔍 Student GET API: Making request to backend...')
    const response = await fetch(`${BACKEND_URL}/api/v1/students/${params.id}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    console.log('🔍 Student GET API: Backend response status:', response.status)
    const data = await response.json()
    console.log('🔍 Student GET API: Backend response data:', data)

    if (!response.ok) {
      console.log('❌ Student GET API: Backend returned error:', response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    console.log('✅ Student GET API: Successfully fetched student data')
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Student GET API: Error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log('🔍 Student PUT API: Starting update for student ID:', params.id)
    
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      console.log('❌ Student PUT API: No access token found')
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    console.log('🔍 Student PUT API: Request body:', body)

    const response = await fetch(`${BACKEND_URL}/api/v1/students/${params.id}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()
    console.log('🔍 Student PUT API: Backend response status:', response.status)
    console.log('🔍 Student PUT API: Backend response data:', data)

    if (!response.ok) {
      console.log('❌ Student PUT API: Backend returned error:', response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    console.log('✅ Student PUT API: Update successful')
    return NextResponse.json(data)
  } catch (error) {
    console.error("❌ Student PUT API: Error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
