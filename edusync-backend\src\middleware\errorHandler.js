const { logger } = require('../utils/logger');

/**
 * Global error handling middleware
 */
exports.errorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // Different error handling for development and production
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, req, res);
  } else {
    // Clone the error to avoid modifying the original
    let error = { ...err };
    error.message = err.message;
    error.name = err.name;

    // Handle specific types of errors
    if (error.name === 'PrismaClientKnownRequestError') {
      if (error.code === 'P2002') {
        error = handleDuplicateFieldsError(error);
      } else if (error.code === 'P2025') {
        error = handleNotFoundError(error);
      }
    }

    if (error.name === 'JsonWebTokenError') {
      error = handleJWTError();
    }
    if (error.name === 'TokenExpiredError') {
      error = handleJWTExpiredError();
    }

    sendErrorProd(error, req, res);
  }
};

/**
 * Send detailed error for development environment
 */
const sendErrorDev = (err, req, res) => {
  logger.error('=== Error Details ===');
  logger.error(`Status Code: ${err.statusCode}`);
  logger.error(`Message: ${err.message}`);
  logger.error(`URL: ${req.originalUrl}`);
  logger.error(`Method: ${req.method}`);
  logger.error('Stack Trace:');
  logger.error(err.stack);
  logger.error('===================');

  return res.status(err.statusCode).json({
    status: err.status,
    error: err,
    message: err.message,
    stack: err.stack,
  });
};

/**
 * Send simplified error for production environment
 */
const sendErrorProd = (err, req, res) => {
  // Log error
  logger.error(`${err.statusCode} - ${err.message} - ${req.originalUrl} - ${req.method}`);

  // Operational errors: send message to client
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
    });
  }

  // Programming or other unknown errors: don't leak error details
  return res.status(500).json({
    status: 'error',
    message: 'Something went wrong',
  });
};

/**
 * Handle duplicate field error (Prisma P2002)
 */
const handleDuplicateFieldsError = (err) => {
  const field = err.meta?.target?.[0] || 'field';
  const message = `Duplicate ${field} value. Please use another value.`;
  
  return new AppError(message, 400, true);
};

/**
 * Handle not found error (Prisma P2025)
 */
const handleNotFoundError = (err) => {
  return new AppError('Resource not found', 404, true);
};

/**
 * Handle JWT error
 */
const handleJWTError = () => {
  return new AppError('Invalid token. Please log in again.', 401, true);
};

/**
 * Handle JWT expired error
 */
const handleJWTExpiredError = () => {
  return new AppError('Your token has expired. Please log in again.', 401, true);
};

/**
 * Custom AppError class is imported from utils
 */
const AppError = require('../utils/appError');
