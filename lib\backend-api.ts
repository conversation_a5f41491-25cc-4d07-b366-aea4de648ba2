// Note: This file cannot use 'use server' directive because it's imported in client components
// For server-side API calls, use Next.js API routes

import { enhancedApiClient } from "@/lib/api-client-enhanced"

/**
 * Constants
 */
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

/**
 * Utility function to safely extract array data from API responses
 */
export function safeArrayFromResponse(response: any, fallback: any[] = []): any[] {
  console.log("🔍 safeArrayFromResponse input:", response)

  if (!response) {
    console.log("❌ safeArrayFromResponse: No response")
    return fallback
  }

  // Handle enhanced API client format
  if (response.success === true && response.data) {
    console.log("✅ safeArrayFromResponse: Enhanced API format with success=true")

    // Check for subjects array in response.data.subjects (subjects endpoint)
    if (response.data.subjects && Array.isArray(response.data.subjects)) {
      console.log("✅ safeArrayFromResponse: Found subjects array")
      return response.data.subjects
    }

    // Check for classes array in response.data.classes (classes endpoint)
    if (response.data.classes && Array.isArray(response.data.classes)) {
      console.log("✅ safeArrayFromResponse: Found classes array")
      return response.data.classes
    }

    // Check for teachers array in response.data.teachers (teachers endpoint)
    if (response.data.teachers && Array.isArray(response.data.teachers)) {
      console.log("✅ safeArrayFromResponse: Found teachers array")
      return response.data.teachers
    }

    // Check for students array in response.data.students (students endpoint)
    if (response.data.students && Array.isArray(response.data.students)) {
      console.log("✅ safeArrayFromResponse: Found students array")
      return response.data.students
    }

    // Fallback to direct data array
    return Array.isArray(response.data) ? response.data : fallback
  }

  // Handle nested success format
  if (response.data && response.data.success === true && response.data.data) {
    console.log("✅ safeArrayFromResponse: Nested success format")
    return Array.isArray(response.data.data) ? response.data.data : fallback
  }

  // Handle direct data format (when response.success is undefined but we have data)
  if (response.data && Array.isArray(response.data)) {
    console.log("✅ safeArrayFromResponse: Direct data array format")
    return response.data
  }

  // Handle case where response itself is the data array
  if (Array.isArray(response)) {
    console.log("✅ safeArrayFromResponse: Response is direct array")
    return response
  }

  // Handle legacy format
  if (response.success !== false && response.data) {
    console.log("✅ safeArrayFromResponse: Legacy format")
    return Array.isArray(response.data) ? response.data : fallback
  }

  console.log("❌ safeArrayFromResponse: No valid data found, using fallback")
  console.log("📊 Response structure:", {
    hasSuccess: 'success' in response,
    successValue: response.success,
    hasData: 'data' in response,
    dataType: typeof response.data,
    isDataArray: Array.isArray(response.data)
  })

  return fallback
}

interface FetchOptions extends RequestInit {
  params?: Record<string, string>
  data?: any
}

/**
 * Global token refresh state to prevent multiple simultaneous refresh attempts
 */
let isRefreshing = false
let refreshPromise: Promise<boolean> | null = null

/**
 * Refresh access token on client side
 */
async function refreshAccessToken(): Promise<boolean> {
  if (isRefreshing && refreshPromise) {
    return refreshPromise
  }

  isRefreshing = true
  refreshPromise = (async () => {
    try {
      console.log("🔄 clientFetch: Attempting token refresh...")
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      })

      if (response.ok) {
        console.log("✅ clientFetch: Token refresh successful")
        return true
      } else {
        console.log("❌ clientFetch: Token refresh failed")
        return false
      }
    } catch (error) {
      console.error("❌ clientFetch: Token refresh error:", error)
      return false
    } finally {
      isRefreshing = false
      refreshPromise = null
    }
  })()

  return refreshPromise
}

/**
 * Helper function to make API requests to the backend
 * This is used by client components
 */
export async function clientFetch(
  endpoint: string,
  { params, data, headers: customHeaders, ...options }: FetchOptions = {}
) {
  // Build URL with query parameters
  const url = new URL(`${BACKEND_URL}${endpoint}`)
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, value)
      }
    })
  }

  // Prepare headers with content type
  const headers = {
    "Content-Type": "application/json",
    ...customHeaders,
  }

  // Make the request via the frontend API proxy
  let apiEndpoint = "/api/auth"
  
  // Handle super-admin routes with specific sub-routes first
  if (endpoint.includes("/super-admin/dashboard")) {
    apiEndpoint = "/api/super-admin/dashboard"
  } else if (endpoint.includes("/super-admin/users")) {
    apiEndpoint = "/api/super-admin/users"
  } else if (endpoint.includes("/super-admin/institutions")) {
    apiEndpoint = "/api/super-admin/institutions"
  } else if (endpoint.includes("/super-admin/subscriptions")) {
    apiEndpoint = "/api/super-admin/subscriptions"
  } else if (endpoint.includes("/super-admin/audit-logs")) {
    apiEndpoint = "/api/super-admin/audit-logs"
  } else if (endpoint.includes("/super-admin/security")) {
    apiEndpoint = "/api/super-admin/security"
  } else if (endpoint.includes("/super-admin/settings")) {
    apiEndpoint = "/api/super-admin/settings"
  } else if (endpoint.includes("/super-admin/support")) {
    apiEndpoint = "/api/super-admin/support"
  } else if (endpoint.includes("/super-admin/analytics")) {
    apiEndpoint = "/api/super-admin/analytics"
  } else if (endpoint.includes("/super-admin/schools")) {
    apiEndpoint = "/api/super-admin/schools"
  } else if (endpoint.includes("/super-admin/content")) {
    apiEndpoint = "/api/super-admin/content"
  } else if (endpoint.includes("/super-admin/features")) {
    apiEndpoint = "/api/super-admin/features"
  } else if (endpoint.includes("/super-admin/pricing")) {
    apiEndpoint = "/api/super-admin/pricing"
  } else if (endpoint.includes("/super-admin/testimonials")) {
    apiEndpoint = "/api/super-admin/testimonials"
  } else if (endpoint.includes("/super-admin")) {
    apiEndpoint = "/api/super-admin"
  } else if (endpoint.includes("/users")) {
    apiEndpoint = "/api/users"
  } else if (endpoint.includes("/institutions")) {
    apiEndpoint = "/api/institutions"
  } else if (endpoint.includes("/schools")) {
    apiEndpoint = "/api/schools"
  } else if (endpoint.includes("/analytics")) {
    apiEndpoint = "/api/analytics"
  } else if (endpoint.includes("/security")) {
    apiEndpoint = "/api/security"
  } else if (endpoint.includes("/audit-logs")) {
    apiEndpoint = "/api/audit-logs"
  } else if (endpoint.includes("/subscriptions")) {
    apiEndpoint = "/api/subscriptions"
  } else if (endpoint.includes("/dashboard")) {
    apiEndpoint = "/api/dashboard"
  } else if (endpoint.includes("/students")) {
    // Handle individual student operations by preserving the ID in the path
    const studentMatch = endpoint.match(/\/students\/([^\/]+)(\/.*)?$/)
    if (studentMatch) {
      const studentId = studentMatch[1]
      const subPath = studentMatch[2] || ""
      apiEndpoint = `/api/students/${studentId}${subPath}`
    } else {
      apiEndpoint = "/api/students"
    }
  } else if (endpoint.includes("/teachers")) {
    apiEndpoint = "/api/teachers"
  } else if (endpoint.includes("/staff")) {
    apiEndpoint = "/api/staff"
  } else if (endpoint.includes("/classes")) {
    apiEndpoint = "/api/classes"
  } else if (endpoint.includes("/subjects")) {
    apiEndpoint = "/api/subjects"
  } else if (endpoint.includes("/attendance")) {
    apiEndpoint = "/api/attendance"
  } else if (endpoint.includes("/grades")) {
    apiEndpoint = "/api/grades"
  } else if (endpoint.includes("/exams")) {
    apiEndpoint = "/api/exams"
  } else if (endpoint.includes("/fees")) {
    apiEndpoint = "/api/fees"
  } else if (endpoint.includes("/academic-calendar")) {
    apiEndpoint = "/api/academic-calendar"
  } else if (endpoint.includes("/academic-years")) {
    apiEndpoint = "/api/academic-years"
  } else if (endpoint.includes("/timetable")) {
    apiEndpoint = "/api/schedules"
  }

  // Build the final API endpoint URL with query parameters for GET requests
  let finalApiEndpoint = apiEndpoint
  if (params && (options.method === "GET" || !options.method)) {
    const urlParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlParams.append(key, String(value))
      }
    })
    if (urlParams.toString()) {
      finalApiEndpoint += `?${urlParams.toString()}`
    }
  }

  // Make the request with automatic retry on 401
  let response = await fetch(finalApiEndpoint, {
    method: options.method || "GET",
    headers: {
      ...headers,
      'X-Original-Endpoint': endpoint, // Pass the original endpoint to the API route
      'X-Query-Params': params ? JSON.stringify(params) : '',
    },
    credentials: "include", // Include cookies
    body: data ? JSON.stringify(data) : undefined,
  })

  // If we get a 401, try to refresh the token and retry once
  if (response.status === 401 && !isRefreshing) {
    console.log("🔄 clientFetch: Got 401, attempting token refresh...")
    const refreshSuccess = await refreshAccessToken()

    if (refreshSuccess) {
      console.log("🔄 clientFetch: Token refreshed, retrying request...")
      // Retry the original request
      response = await fetch(finalApiEndpoint, {
        method: options.method || "GET",
        headers: {
          ...headers,
          'X-Original-Endpoint': endpoint,
          'X-Query-Params': params ? JSON.stringify(params) : '',
        },
        credentials: "include",
        body: data ? JSON.stringify(data) : undefined,
      })
    } else {
      console.log("❌ clientFetch: Token refresh failed, redirecting to login...")
      // Redirect to login if refresh fails
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
    }
  }

  // Parse the response
  try {
    const contentType = response.headers.get("content-type")
    
    if (contentType && contentType.includes("application/json")) {
      const result = await response.json()
      
      if (!response.ok) {
        return {
          success: false,
          status: response.status,
          error: result.message || result.error || "An unexpected error occurred",
          data: null,
        }
      }
      
      return {
        success: true,
        status: response.status,
        data: result.data || result,
        message: result.message,
      }
    } else {
      const text = await response.text()
      
      if (!response.ok) {
        return {
          success: false,
          status: response.status,
          error: text || "An unexpected error occurred",
          data: null,
        }
      }
      
      return {
        success: true,
        status: response.status,
        data: text,
      }
    }
  } catch (error) {
    return {
      success: false,
      status: response.status,
      error: "Failed to parse response",
      data: null,
    }
  }
}

/**
 * Authentication API service
 */
export const authService = {
  /**
   * Login user
   */
  login: async (email: string, password: string) => {
    return await clientFetch("/api/v1/auth/login", {
      method: "POST",
      data: { 
        action: "login",
        email, 
        password 
      },
    })
  },

  /**
   * Refresh access token
   */
  refreshToken: async () => {
    return await clientFetch("/api/v1/auth/refresh-token", {
      method: "POST",
      data: { action: "refresh" },
    })
  },

  /**
   * Logout user
   */
  logout: async () => {
    return await clientFetch("/api/v1/auth/logout", {
      method: "POST",
      data: { action: "logout" },
    })
  },

  /**
   * Get current user profile
   */
  getProfile: async () => {
    return await clientFetch("/api/v1/auth/me", {
      method: "GET",
      params: { action: "profile" },
    })
  },

  /**
   * Request password reset
   */
  requestPasswordReset: async (email: string) => {
    return await clientFetch("/api/auth", {
      method: "POST",
      data: { 
        action: "password-reset-request",
        email 
      },
    })
  },

  /**
   * Reset password using token
   */
  resetPassword: async (token: string, password: string, confirmPassword: string) => {
    return await clientFetch("/api/auth", {
      method: "POST",
      data: { 
        action: "password-reset-confirm",
        token,
        password, 
        confirmPassword 
      },
    })
  },

  /**
   * Verify email using token
   */
  verifyEmail: async (token: string) => {
    return await clientFetch("/api/auth", {
      method: "POST",
      data: {
        action: "verify-email",
        token
      }
    })
  },

  /**
   * Change password
   */
  changePassword: async (currentPassword: string, newPassword: string, confirmPassword: string) => {
    return await clientFetch("/api/v1/auth/change-password", {
      method: "POST",
      data: { 
        action: "change-password",
        current_password: currentPassword, 
        new_password: newPassword, 
        confirm_password: confirmPassword 
      },
    })
  },
}

/**
 * User API service
 */
export const userService = {
  /**
   * Create a new user (admin only)
   */
  createUser: async (userData: any) => {
    return await clientFetch("/api/v1/users", {
      method: "POST",
      data: userData,
    })
  },

  /**
   * Get users (admin only)
   */
  getUsers: async (params?: any) => {
    return await clientFetch("/api/v1/users", {
      method: "GET",
      params,
    })
  },

  /**
   * Get user by ID
   */
  getUserById: async (id: string) => {
    return await clientFetch(`/api/v1/users/${id}`, {
      method: "GET",
    })
  },

  /**
   * Update user
   */
  updateUser: async (id: string, userData: any) => {
    return await clientFetch(`/api/v1/users/${id}`, {
      method: "PUT",
      data: userData,
    })
  },

  /**
   * Delete user (admin only)
   */
  deleteUser: async (id: string) => {
    return await clientFetch(`/api/v1/users/${id}`, {
      method: "DELETE",
    })
  },

  /**
   * Get users by institution
   */
  getUsersByInstitution: async (institutionId: string, params?: any) => {
    return await clientFetch(`/api/v1/users/institution/${institutionId}`, {
      method: "GET",
      params,
    })
  },

  /**
   * Get users by school
   */
  getUsersBySchool: async (schoolId: string, params?: any) => {
    return await clientFetch(`/api/v1/users/school/${schoolId}`, {
      method: "GET",
      params,
    })
  },
}

/**
 * Institution API service
 */
export const institutionService = {
  /**
   * Get all institutions (super-admin only)
   */
  getAllInstitutions: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/institutions", {
      method: "GET",
      params,
    })
  },

  /**
   * Get institution by ID
   */
  getInstitutionById: async (id: string) => {
    return await clientFetch(`/api/v1/institutions/${id}`, {
      method: "GET",
    })
  },

  /**
   * Create institution
   */
  createInstitution: async (institutionData: any) => {
    return await clientFetch("/api/v1/institutions", {
      method: "POST",
      data: institutionData,
    })
  },

  /**
   * Update institution
   */
  updateInstitution: async (id: string, institutionData: any) => {
    return await clientFetch(`/api/v1/institutions/${id}`, {
      method: "PATCH",
      data: institutionData,
    })
  },

  /**
   * Delete institution (super-admin only)
   */
  deleteInstitution: async (id: string) => {
    return await clientFetch(`/api/v1/institutions/${id}`, {
      method: "DELETE",
    })
  },

  /**
   * Approve institution (super-admin only)
   */
  approveInstitution: async (id: string) => {
    return await clientFetch(`/api/v1/super-admin/institutions/${id}/approve`, {
      method: "PATCH",
    })
  },

  /**
   * Suspend institution (super-admin only)
   */
  suspendInstitution: async (id: string) => {
    return await clientFetch(`/api/v1/super-admin/institutions/${id}/suspend`, {
      method: "PATCH",
    })
  },
}

/**
 * Super Admin API service
 */
export const superAdminService = {
  /**
   * Dashboard Analytics
   */
  getDashboardAnalytics: async () => {
    return await clientFetch("/api/v1/super-admin/dashboard", {
      method: "GET",
    })
  },

  /**
   * Get recent activities
   */
  getRecentActivities: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/activities", {
      method: "GET",
      params,
    })
  },

  /**
   * Get pending institutions
   */
  getPendingInstitutions: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/institutions/pending", {
      method: "GET",
      params,
    })
  },

  /**
   * Analytics
   */
  getAnalytics: async (type?: string, params?: any) => {
    const endpoint = type ? `/api/v1/super-admin/analytics/${type}` : "/api/v1/super-admin/analytics"
    return await clientFetch(endpoint, {
      method: "GET",
      params,
    })
  },

  /**
   * User Management
   */
  getAllUsers: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/users", {
      method: "GET",
      params,
    })
  },

  createUser: async (userData: any) => {
    return await clientFetch("/api/v1/super-admin/users", {
      method: "POST",
      data: userData,
    })
  },

  updateUser: async (id: string, userData: any) => {
    return await clientFetch(`/api/v1/super-admin/users/${id}`, {
      method: "PATCH",
      data: userData,
    })
  },

  deleteUser: async (id: string) => {
    return await clientFetch(`/api/v1/super-admin/users/${id}`, {
      method: "DELETE",
    })
  },

  suspendUser: async (id: string) => {
    return await clientFetch(`/api/v1/super-admin/users/${id}/suspend`, {
      method: "PATCH",
    })
  },

  activateUser: async (id: string) => {
    return await clientFetch(`/api/v1/super-admin/users/${id}/activate`, {
      method: "PATCH",
    })
  },

  /**
   * Security Management
   */
  getSecuritySettings: async () => {
    return await clientFetch("/api/v1/super-admin/security/settings", {
      method: "GET",
    })
  },

  updateSecuritySettings: async (settings: any) => {
    return await clientFetch("/api/v1/super-admin/security/settings", {
      method: "PATCH",
      data: settings,
    })
  },

  getSecurityLogs: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/security/logs", {
      method: "GET",
      params,
    })
  },

  /**
   * Audit Logs
   */
  getAuditLogs: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/audit-logs", {
      method: "GET",
      params,
    })
  },

  /**
   * Subscription Management
   */
  getAllSubscriptions: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/subscriptions", {
      method: "GET",
      params,
    })
  },

  updateSubscription: async (id: string, subscriptionData: any) => {
    return await clientFetch(`/api/v1/super-admin/subscriptions/${id}`, {
      method: "PATCH",
      data: subscriptionData,
    })
  },

  /**
   * Content Management
   */
  getFeatures: async () => {
    return await clientFetch("/api/v1/super-admin/content/features", {
      method: "GET",
    })
  },

  updateFeatures: async (features: any) => {
    return await clientFetch("/api/v1/super-admin/content/features", {
      method: "PATCH",
      data: features,
    })
  },

  getPricing: async () => {
    return await clientFetch("/api/v1/super-admin/content/pricing", {
      method: "GET",
    })
  },

  updatePricing: async (pricing: any) => {
    return await clientFetch("/api/v1/super-admin/content/pricing", {
      method: "PATCH",
      data: pricing,
    })
  },

  getTestimonials: async () => {
    return await clientFetch("/api/v1/super-admin/content/testimonials", {
      method: "GET",
    })
  },

  updateTestimonials: async (testimonials: any) => {
    return await clientFetch("/api/v1/super-admin/content/testimonials", {
      method: "PATCH",
      data: testimonials,
    })
  },

  /**
   * Support Management
   */
  getSupportTickets: async (params?: any) => {
    return await clientFetch("/api/v1/super-admin/support/tickets", {
      method: "GET",
      params,
    })
  },

  updateSupportTicket: async (id: string, ticketData: any) => {
    return await clientFetch(`/api/v1/super-admin/support/tickets/${id}`, {
      method: "PATCH",
      data: ticketData,
    })
  },

  /**
   * System Settings
   */
  getSystemSettings: async () => {
    return await clientFetch("/api/v1/super-admin/settings", {
      method: "GET",
    })
  },

  updateSystemSettings: async (settings: any) => {
    return await clientFetch("/api/v1/super-admin/settings", {
      method: "PATCH",
      data: settings,
    })
  },
}

/**
 * Dashboard API service
 */
export const dashboardService = {
  getStats: async (institutionId?: string, schoolId?: string) => {
    const params: Record<string, string> = {}
    if (institutionId) params.institutionId = institutionId
    if (schoolId) params.schoolId = schoolId
    
    return await clientFetch("/api/v1/dashboard/stats", {
      method: "GET",
      params,
    })
  },

  getRecentActivities: async (institutionId?: string, limit = 5) => {
    const params: Record<string, string> = { limit: limit.toString() }
    if (institutionId) params.institutionId = institutionId
    
    return await clientFetch("/api/v1/dashboard/activities", {
      method: "GET",
      params,
    })
  },

  getOverview: async (institutionId?: string, schoolId?: string) => {
    const params: Record<string, string> = {}
    if (institutionId) params.institutionId = institutionId
    if (schoolId) params.schoolId = schoolId
    
    return await clientFetch("/api/v1/dashboard/overview", {
      method: "GET",
      params,
    })
  },
}

/**
 * Student API service
 */
export const studentService = {
  getStudents: async (params?: any) => {
    return await clientFetch("/api/v1/students", {
      method: "GET",
      params,
    })
  },

  getStudentById: async (id: string) => {
    return await clientFetch(`/api/v1/students/${id}`, {
      method: "GET",
    })
  },

  createStudent: async (studentData: any) => {
    return await clientFetch("/api/v1/students", {
      method: "POST",
      data: studentData,
    })
  },

  updateStudent: async (id: string, studentData: any) => {
    return await clientFetch(`/api/v1/students/${id}`, {
      method: "PUT",
      data: studentData,
    })
  },

  deleteStudent: async (id: string) => {
    return await clientFetch(`/api/v1/students/${id}`, {
      method: "DELETE",
    })
  },

  getStudentsByClass: async (classId: string, params?: any) => {
    return await clientFetch(`/api/v1/classes/${classId}/students`, {
      method: "GET",
      params,
    })
  },

  getStudentGrades: async (studentId: string, params?: any) => {
    return await clientFetch(`/api/v1/students/${studentId}/grades`, {
      method: "GET",
      params,
    })
  },

  getStudentAttendance: async (studentId: string, params?: any) => {
    return await clientFetch(`/api/v1/students/${studentId}/attendance`, {
      method: "GET",
      params,
    })
  },
}

/**
 * Teacher API service
 */
export const teacherService = {
  getTeachers: async (params?: any) => {
    return await clientFetch("/api/v1/teachers", {
      method: "GET",
      params,
    })
  },

  getTeacherById: async (id: string) => {
    return await clientFetch(`/api/v1/teachers/${id}`, {
      method: "GET",
    })
  },

  createTeacher: async (teacherData: any) => {
    return await clientFetch("/api/v1/teachers", {
      method: "POST",
      data: teacherData,
    })
  },

  updateTeacher: async (id: string, teacherData: any) => {
    return await clientFetch(`/api/v1/teachers/${id}`, {
      method: "PATCH",
      data: teacherData,
    })
  },

  deleteTeacher: async (id: string) => {
    return await clientFetch(`/api/v1/teachers/${id}`, {
      method: "DELETE",
    })
  },

  getTeacherClasses: async (teacherId: string) => {
    return await clientFetch(`/api/v1/teachers/${teacherId}/classes`, {
      method: "GET",
    })
  },

  getTeacherTimetable: async (teacherId: string) => {
    return await clientFetch(`/api/v1/teachers/${teacherId}/timetable`, {
      method: "GET",
    })
  },
}

/**
 * Staff API service
 */
export const staffService = {
  getStaff: async (params?: any) => {
    return await clientFetch("/api/v1/staff", {
      method: "GET",
      params,
    })
  },

  getStaffById: async (id: string) => {
    return await clientFetch(`/api/v1/staff/${id}`, {
      method: "GET",
    })
  },

  createStaff: async (staffData: any) => {
    return await clientFetch("/api/v1/staff", {
      method: "POST",
      data: staffData,
    })
  },

  updateStaff: async (id: string, staffData: any) => {
    return await clientFetch(`/api/v1/staff/${id}`, {
      method: "PATCH",
      data: staffData,
    })
  },

  deleteStaff: async (id: string) => {
    return await clientFetch(`/api/v1/staff/${id}`, {
      method: "DELETE",
    })
  },
}

/**
 * Class API service with enhanced token management
 */
export const classService = {
  // Enhanced methods with automatic token refresh
  getClasses: async (params?: any) => {
    return await enhancedApiClient.get("/api/classes", params)
  },

  getClassById: async (id: string) => {
    return await enhancedApiClient.get(`/api/classes/${id}`)
  },

  createClass: async (classData: any) => {
    return await enhancedApiClient.post("/api/classes", classData)
  },

  updateClass: async (id: string, classData: any) => {
    return await enhancedApiClient.put(`/api/classes?id=${id}`, classData)
  },

  deleteClass: async (id: string) => {
    return await enhancedApiClient.delete(`/api/classes?id=${id}`)
  },

  // Legacy methods for fallback
  getClassesLegacy: async (params?: any) => {
    return await clientFetch("/api/v1/classes", {
      method: "GET",
      params,
    })
  },

  getClassStudents: async (classId: string) => {
    return await clientFetch(`/api/v1/classes/${classId}/students`, {
      method: "GET",
    })
  },

  getClassTimetable: async (classId: string) => {
    return await clientFetch(`/api/v1/classes/${classId}/timetable`, {
      method: "GET",
    })
  },
}

/**
 * Subject API service with enhanced token management
 */
export const subjectService = {
  // Enhanced method with automatic token refresh
  getSubjects: async (params?: any) => {
    return await enhancedApiClient.get("/api/subjects", params)
  },

  // Legacy method for fallback
  getSubjectsLegacy: async (params?: any) => {
    return await clientFetch("/api/v1/subjects", {
      method: "GET",
      params,
    })
  },

  getSubjectById: async (id: string) => {
    return await clientFetch(`/api/v1/subjects/${id}`, {
      method: "GET",
    })
  },

  createSubject: async (subjectData: any) => {
    return await clientFetch("/api/v1/subjects", {
      method: "POST",
      data: subjectData,
    })
  },

  updateSubject: async (id: string, subjectData: any) => {
    return await clientFetch(`/api/v1/subjects/${id}`, {
      method: "PATCH",
      data: subjectData,
    })
  },

  deleteSubject: async (id: string) => {
    return await clientFetch(`/api/v1/subjects/${id}`, {
      method: "DELETE",
    })
  },
}

/**
 * Attendance API service
 */
export const attendanceService = {
  getAttendance: async (params?: any) => {
    return await clientFetch("/api/v1/attendance", {
      method: "GET",
      params,
    })
  },

  markAttendance: async (attendanceData: any) => {
    return await clientFetch("/api/v1/attendance", {
      method: "POST",
      data: attendanceData,
    })
  },

  updateAttendance: async (id: string, attendanceData: any) => {
    return await clientFetch(`/api/v1/attendance/${id}`, {
      method: "PATCH",
      data: attendanceData,
    })
  },

  getClassAttendance: async (classId: string, date: string) => {
    return await clientFetch(`/api/v1/classes/${classId}/attendance`, {
      method: "GET",
      params: { date },
    })
  },

  getStudentAttendance: async (studentId: string, params?: any) => {
    return await clientFetch(`/api/v1/students/${studentId}/attendance`, {
      method: "GET",
      params,
    })
  },
}

/**
 * Grades API service
 */
export const gradeService = {
  getGrades: async (params?: any) => {
    return await clientFetch("/api/v1/grades", {
      method: "GET",
      params,
    })
  },

  createGrade: async (gradeData: any) => {
    return await clientFetch("/api/v1/grades", {
      method: "POST",
      data: gradeData,
    })
  },

  updateGrade: async (id: string, gradeData: any) => {
    return await clientFetch(`/api/v1/grades/${id}`, {
      method: "PATCH",
      data: gradeData,
    })
  },

  deleteGrade: async (id: string) => {
    return await clientFetch(`/api/v1/grades/${id}`, {
      method: "DELETE",
    })
  },

  getStudentGrades: async (studentId: string, params?: any) => {
    return await clientFetch(`/api/v1/students/${studentId}/grades`, {
      method: "GET",
      params,
    })
  },

  getClassGrades: async (classId: string, params?: any) => {
    return await clientFetch(`/api/v1/classes/${classId}/grades`, {
      method: "GET",
      params,
    })
  },
}

/**
 * Exam API service
 */
export const examService = {
  getExams: async (params?: any) => {
    return await clientFetch("/api/v1/exams", {
      method: "GET",
      params,
    })
  },

  getExamById: async (id: string) => {
    return await clientFetch(`/api/v1/exams/${id}`, {
      method: "GET",
    })
  },

  createExam: async (examData: any) => {
    return await clientFetch("/api/v1/exams", {
      method: "POST",
      data: examData,
    })
  },

  updateExam: async (id: string, examData: any) => {
    return await clientFetch(`/api/v1/exams/${id}`, {
      method: "PATCH",
      data: examData,
    })
  },

  deleteExam: async (id: string) => {
    return await clientFetch(`/api/v1/exams/${id}`, {
      method: "DELETE",
    })
  },

  getExamResults: async (examId: string, params?: any) => {
    return await clientFetch(`/api/v1/exams/${examId}/results`, {
      method: "GET",
      params,
    })
  },

  createExamResult: async (examId: string, resultData: any) => {
    return await clientFetch(`/api/v1/exams/${examId}/results`, {
      method: "POST",
      data: resultData,
    })
  },
}

/**
 * Fee API service
 */
export const feeService = {
  getFeeStructures: async (params?: any) => {
    return await clientFetch("/api/v1/fees/structures", {
      method: "GET",
      params,
    })
  },

  createFeeStructure: async (feeData: any) => {
    return await clientFetch("/api/v1/fees/structures", {
      method: "POST",
      data: feeData,
    })
  },

  getFeePayments: async (params?: any) => {
    return await clientFetch("/api/v1/fees/payments", {
      method: "GET",
      params,
    })
  },

  createFeePayment: async (paymentData: any) => {
    return await clientFetch("/api/v1/fees/payments", {
      method: "POST",
      data: paymentData,
    })
  },

  getStudentFees: async (studentId: string) => {
    return await clientFetch(`/api/v1/students/${studentId}/fees`, {
      method: "GET",
    })
  },

  getFeeReport: async (params?: any) => {
    return await clientFetch("/api/v1/fees/report", {
      method: "GET",
      params,
    })
  },
}

/**
 * Academic Calendar API service
 */
export const academicCalendarService = {
  getEvents: async (params?: any) => {
    return await clientFetch("/api/v1/academic-calendar", {
      method: "GET",
      params,
    })
  },

  createEvent: async (eventData: any) => {
    return await clientFetch("/api/v1/academic-calendar", {
      method: "POST",
      data: eventData,
    })
  },

  updateEvent: async (id: string, eventData: any) => {
    return await clientFetch(`/api/v1/academic-calendar/${id}`, {
      method: "PATCH",
      data: eventData,
    })
  },

  deleteEvent: async (id: string) => {
    return await clientFetch(`/api/v1/academic-calendar/${id}`, {
      method: "DELETE",
    })
  },
}

/**
 * Timetable API service
 */
export const timetableService = {
  getTimetable: async (params?: any) => {
    return await clientFetch("/api/v1/timetable", {
      method: "GET",
      params,
    })
  },

  createTimetableSlot: async (slotData: any) => {
    return await clientFetch("/api/v1/timetable", {
      method: "POST",
      data: slotData,
    })
  },

  updateTimetableSlot: async (id: string, slotData: any) => {
    return await clientFetch(`/api/v1/timetable/${id}`, {
      method: "PATCH",
      data: slotData,
    })
  },

  deleteTimetableSlot: async (id: string) => {
    return await clientFetch(`/api/v1/timetable/${id}`, {
      method: "DELETE",
    })
  },

  getClassTimetable: async (classId: string) => {
    return await clientFetch(`/api/v1/classes/${classId}/timetable`, {
      method: "GET",
    })
  },

  getTeacherTimetable: async (teacherId: string) => {
    return await clientFetch(`/api/v1/teachers/${teacherId}/timetable`, {
      method: "GET",
    })
  },
}

/**
 * Academic Year API service
 */
export const academicYearService = {
  getAcademicYears: async (params?: any) => {
    return await clientFetch("/api/v1/academic-years", {
      method: "GET",
      params,
    })
  },

  createAcademicYear: async (yearData: any) => {
    return await clientFetch("/api/v1/academic-years", {
      method: "POST",
      data: yearData,
    })
  },

  updateAcademicYear: async (id: string, yearData: any) => {
    return await clientFetch(`/api/v1/academic-years/${id}`, {
      method: "PATCH",
      data: yearData,
    })
  },

  setActiveAcademicYear: async (id: string) => {
    return await clientFetch(`/api/v1/academic-years/${id}/activate`, {
      method: "PATCH",
    })
  },
}

/**
 * Settings API service
 */
export const settingsService = {
  getInstitutionSettings: async (institutionId: string) => {
    return await clientFetch(`/api/v1/institutions/${institutionId}/settings`, {
      method: "GET",
    })
  },

  updateInstitutionSettings: async (institutionId: string, settings: any) => {
    return await clientFetch(`/api/v1/institutions/${institutionId}/settings`, {
      method: "PATCH",
      data: settings,
    })
  },

  getSchoolSettings: async (schoolId: string) => {
    return await clientFetch(`/api/v1/schools/${schoolId}/settings`, {
      method: "GET",
    })
  },

  updateSchoolSettings: async (schoolId: string, settings: any) => {
    return await clientFetch(`/api/v1/schools/${schoolId}/settings`, {
      method: "PATCH",
      data: settings,
    })
  },

  getSystemSettings: async () => {
    return await clientFetch("/api/v1/settings/system", {
      method: "GET",
    })
  },

  updateSystemSettings: async (settings: any) => {
    return await clientFetch("/api/v1/settings/system", {
      method: "PATCH",
      data: settings,
    })
  },
}

/**
 * Report API service
 */
export const reportService = {
  getReports: async (params?: any) => {
    return await clientFetch("/api/v1/reports", {
      method: "GET",
      params,
    })
  },

  generateReport: async (reportData: any) => {
    return await clientFetch("/api/v1/reports/generate", {
      method: "POST",
      data: reportData,
    })
  },

  getReportById: async (id: string) => {
    return await clientFetch(`/api/v1/reports/${id}`, {
      method: "GET",
    })
  },

  downloadReport: async (id: string) => {
    return await clientFetch(`/api/v1/reports/${id}/download`, {
      method: "GET",
    })
  },
}

/**
 * Notification API service
 */
export const notificationService = {
  getNotifications: async (params?: any) => {
    return await clientFetch("/api/v1/notifications", {
      method: "GET",
      params,
    })
  },

  createNotification: async (notificationData: any) => {
    return await clientFetch("/api/v1/notifications", {
      method: "POST",
      data: notificationData,
    })
  },

  markAsRead: async (id: string) => {
    return await clientFetch(`/api/v1/notifications/${id}/read`, {
      method: "PATCH",
    })
  },

  markAllAsRead: async () => {
    return await clientFetch("/api/v1/notifications/read-all", {
      method: "PATCH",
    })
  },
}

/**
 * Unified backend API object containing all services
 */
export const backendApi = {
  auth: authService,
  user: userService,
  institution: institutionService,
  superAdmin: superAdminService,
  dashboard: dashboardService,
  student: studentService,
  teacher: teacherService,
  staff: staffService,
  class: classService,
  subject: subjectService,
  attendance: attendanceService,
  grade: gradeService,
  exam: examService,
  fee: feeService,
  academicCalendar: academicCalendarService,
  timetable: timetableService,
  academicYear: academicYearService,
  settings: settingsService,
  report: reportService,
  notification: notificationService,
  institutions: institutionService,
  users: userService,
  reports: reportService,
  notifications: notificationService,
}
